#!/usr/bin/env bash

_git-up-internal() {
    if ! command git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
        printf "Error: Not a git repository.\n" >&2
        return 1
    fi

    local remote="${GIT_REMOTE:-origin}"
    printf "Using remote: %s\n" "$remote"

    local current_branch
    current_branch=$(command git symbolic-ref --short HEAD 2>/dev/null) || current_branch=$(command git rev-parse --abbrev-ref HEAD 2>/dev/null)
    if [[ -z "$current_branch" ]] || [[ "$current_branch" == "HEAD" ]]; then
        printf "Error: Cannot determine current branch or HEAD is detached.\n" >&2
        return 1
    fi

    printf "Current branch: %s\n" "$current_branch"
    printf "Fetching from %s...\n" "$remote"
    if ! command git fetch "$remote" --prune; then
        printf "Error: Failed to fetch from remote '%s'.\n" "$remote" >&2
        return 1
    fi

    local remote_ref="refs/remotes/$remote/$current_branch"
    if ! command git show-ref --verify --quiet "$remote_ref"; then
        printf "Branch '%s' does not have a tracking branch on remote '%s', skipping update.\n" "$current_branch" "$remote"
        return 0
    fi

    local local_commit remote_commit base
    local_commit=$(command git rev-parse HEAD)
    remote_commit=$(command git rev-parse "$remote_ref")
    if [[ "$local_commit" == "$remote_commit" ]]; then
        printf "Branch '%s' is already up to date with '%s/%s'.\n" "$current_branch" "$remote" "$current_branch"
        return 0
    fi
    if command git merge-base --is-ancestor "$remote_commit" HEAD; then
        base=$(command git merge-base HEAD "$remote_ref")
        if [[ "$base" == "$remote_commit" ]] && [[ "$local_commit" != "$remote_commit" ]]; then
            printf "Branch '%s' is ahead of '%s/%s'. No pull needed.\n" "$current_branch" "$remote" "$current_branch"
            return 0
        fi
        printf "Branch '%s' has diverged from '%s/%s'. Proceeding with rebase.\n" "$current_branch" "$remote" "$current_branch"
    fi

    local stash_created=1
    local stash_index="stash@{0}"
    local stash_message
    stash_message="git-up auto-stash on $current_branch at $(date +"%Y-%m-%d %H:%M:%S")"

    if [[ -n "$(command git status --porcelain=v1)" ]]; then
        printf "Local changes detected. Stashing...\n" >&2
        if command git stash push --include-untracked --message "$stash_message"; then
            stash_created=0
            printf "Stashed changes with message: %s (%s)\n" "$stash_message" "$stash_index" >&2
        else
            printf "Error: Failed to stash changes. Aborting update.\n" >&2
            return 1
        fi
    else
        printf "Working directory clean. No stash needed.\n"
    fi

    printf "Updating branch '%s' by rebasing onto '%s/%s'...\n" "$current_branch" "$remote" "$current_branch"
    local pull_success=1

    if command git pull --rebase "$remote" "$current_branch"; then
        printf "Update successful.\n"
        pull_success=0
    else
        local pull_exit_code=$?
        printf "Error: 'git pull --rebase' failed (exit code %d). Aborting rebase...\n" "$pull_exit_code" >&2
        if command git rebase --abort; then
            printf "Rebase aborted successfully.\n" >&2
        else
            printf "Warning: 'git rebase --abort' failed. May not have been in rebase state.\n" >&2
        fi
        pull_success=$pull_exit_code
    fi

    local final_exit_code=$pull_success

    if [[ $stash_created -eq 0 ]]; then
        if [[ $pull_success -eq 0 ]]; then
            printf "Applying stashed changes (%s)...\n" "$stash_index" >&2
        else
            printf "Restoring stashed changes (%s) after failed update...\n" "$stash_index" >&2
        fi

        if command git stash apply "$stash_index"; then
            printf "Stashed changes applied successfully.\n" >&2
            printf "Dropping applied stash (%s)...\n" "$stash_index" >&2
            if ! command git stash drop "$stash_index"; then
                local drop_exit_code=$?
                printf "Warning: Failed to drop stash '%s' (exit code %d) after successful apply.\n" "$stash_index" "$drop_exit_code" >&2
                if [[ $final_exit_code -eq 0 ]]; then
                    final_exit_code=$drop_exit_code
                fi
            fi
        else
            local apply_exit_code=$?
            printf "\nWarning: Failed to apply stashed changes (exit code %d).\n" "$apply_exit_code" >&2
            printf "Your changes remain in the stash ('%s').\n" "$stash_index" >&2
            printf "You may need to resolve conflicts and apply manually ('git stash apply %s').\n" "$stash_index" >&2

            if [[ $final_exit_code -eq 0 ]]; then
                final_exit_code=$apply_exit_code
            fi
        fi
    fi

    if [[ $final_exit_code -eq 0 ]]; then
        printf "git-up completed successfully.\n"
    else
        printf "git-up finished with errors (exit code %d).\n" "$final_exit_code" >&2
    fi

    return "$final_exit_code"
}

git-up() {
    local target_dir="${1:-.}"
    local exit_code=0

    if [[ ! -d "$target_dir" ]]; then
        printf "Error: Directory '%s' does not exist.\n" "$target_dir" >&2
        return 1
    fi

    if ! pushd "$target_dir" >/dev/null; then
        printf "Error: Could not change directory to '%s'.\n" "$target_dir" >&2
        return 1
    fi

    _git-up-internal
    exit_code=$?

    if ! popd >/dev/null; then
        printf "Error: Could not change back to original directory from '%s' using popd.\n" "$(pwd)" >&2
        [[ $exit_code -eq 0 ]] && exit_code=1
    fi

    return "$exit_code"
}

git-up "$@"
