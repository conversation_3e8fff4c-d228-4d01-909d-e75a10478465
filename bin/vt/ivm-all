#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/ivm-utils"
source "$(dirname "$0")/all-utils"

IVM_DISTRO_LIST=("debian" "ubuntu" "fedora" "arch" "tw")

all_list() {
    for vm in "${IVM_DISTRO_LIST[@]}"; do
        if ivm_exists "${vm}-ivm"; then
            incus list "${vm}-ivm" --format=compact --columns=ns4
        else
            warn "VM: ${vm}-ivm does not exist"
        fi
    done
}

all_create() {
    slog "Creating Incus VMs (Debian, Ubuntu, Fedora, Arch, Tumbleweed)..."

    for distro in "${IVM_DISTRO_LIST[@]}"; do
        if ivm_exists "${distro}-ivm"; then
            slog "$distro VM already exists, skipping..."
        else
            slog "Creating $distro VM: $distro-ivm"
            ivm-create --distro "$distro" --name "${distro}-ivm"
        fi
    done

    success "All VMs created successfully!"
    slog "You can access them using: incus console <vm-name> <username>"
}

all_delete() {
    slog "Deleting all VMs..."
    for vm in "${IVM_DISTRO_LIST[@]}"; do
        if ivm_exists "${vm}-ivm"; then
            slog "Deleting VM: ${vm}-ivm"
            ivm delete "${vm}-ivm"
        fi
    done
    success "All VMs deleted successfully!"
}

all_start() {
    slog "Starting all VMs..."
    for vm in "${IVM_DISTRO_LIST[@]}"; do
        if ivm_exists "${vm}-ivm"; then
            slog "Starting VM: ${vm}-ivm"
            ivm start "${vm}-ivm"
        fi
    done
    success "All VMs started successfully!"
}

all_stop() {
    slog "Stopping all VMs..."
    for vm in "${IVM_DISTRO_LIST[@]}"; do
        if ivm_exists "${vm}-ivm"; then
            slog "Stopping VM: ${vm}-ivm"
            ivm stop "${vm}-ivm"
        fi
    done
    success "All VMs stopped successfully!"
}

all_restart() {
    slog "Restarting all VMs..."
    for vm in "${IVM_DISTRO_LIST[@]}"; do
        if ivm_exists "${vm}-ivm"; then
            slog "Restarting VM: ${vm}-ivm"
            ivm restart "${vm}-ivm"
        fi
    done
    success "All VMs restarted successfully!"
}

usage() {
    all_usage "incus" "VMs"
}

main() {
    ivm_check_exists_prerequisites
    all_parse_args "$@"
}

main "$@"
