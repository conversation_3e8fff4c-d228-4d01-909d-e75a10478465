#!/usr/bin/env bash
set -e
set -o pipefail

TARGET_USER="root"
DAEMON_NAME="incus"
SUBID_START=100000
SUBID_COUNT=65536

GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

info() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1" >&2
}

if [[ "$(id -u)" -ne 0 ]]; then
  error "This script modifies system files and restarts services."
  error "Please run it with sudo: sudo ./check_incus_idmap.sh"
  exit 1
fi

info "Checking idmap configuration for user '${TARGET_USER}'..."

if [ -f /etc/subuid ] && grep -q "^${TARGET_USER}:" /etc/subuid &&
  [ -f /etc/subgid ] && grep -q "^${TARGET_USER}:" /etc/subgid; then
  info "Configuration for '${TARGET_USER}' is already correct in /etc/subuid and /etc/subgid."
  info "Incus idmap setup is OK."
  exit 0
fi

warn "Incus idmap configuration for '${TARGET_USER}' is missing or incomplete. Applying fix..."

SUBID_END=$((SUBID_START + SUBID_COUNT - 1))
SUBID_RANGE="${SUBID_START}-${SUBID_END}"

info "Adding subordinate ID range ${SUBID_RANGE} for user '${TARGET_USER}'..."
if usermod --add-subuids "${SUBID_RANGE}" --add-subgids "${SUBID_RANGE}" "${TARGET_USER}"; then
  info "Successfully added subordinate ID entries."
else
  error "Failed to run usermod. Please check for errors above."
  exit 1
fi

info "Restarting the '${DAEMON_NAME}' daemon to apply changes..."
if systemctl restart "${DAEMON_NAME}"; then
  info "Daemon '${DAEMON_NAME}' restarted successfully."
else
  error "Failed to restart the '${DAEMON_NAME}' daemon. Check its status with:"
  error "  systemctl status ${DAEMON_NAME}"
  error "  journalctl -u ${DAEMON_NAME} -n 100"
  exit 1
fi

if systemctl is-active --quiet "${DAEMON_NAME}"; then
  echo
  info "${GREEN}Success! The idmap has been configured and the Incus daemon is running.${NC}"
  info "You should now be able to launch containers."
else
  error "Something went wrong. The '${DAEMON_NAME}' daemon is not active after restart."
  exit 1
fi

exit 0
