#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/vm-utils"
source "$(dirname "$0")/all-utils"

VM_EASY_DISTRO_LIST=("ubuntu" "fedora" "arch" "debian" "tumbleweed")

all_create() {
  slog "Creating 4 libvirt VMs (Ubuntu, Fedora, Arch, Debian)..."

  for distro in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${distro}-vme"; then
      slog "$distro VM already exists, skipping..."
    else
      slog "Creating $distro VM: $distro-vme"
      vm-vme --distro "$distro" --name "${distro}-vme"
    fi
  done

  success "All VMs created successfully!"
  slog "You can access them using: virsh console <vm-name>"
}

all_delete() {
  slog "Deleting all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-vme"; then
      slog "Deleting VM: ${vm}-vme"
      vm delete "${vm}-vme"
    fi
  done
  success "All VMs deleted successfully!"
}

all_start() {
  slog "Starting all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-vme"; then
      slog "Starting VM: ${vm}-vme"
      vm start "${vm}-vme"
    fi
  done
  success "All VMs started successfully!"
}

all_stop() {
  slog "Stopping all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-vme"; then
      slog "Stopping VM: ${vm}-vme"
      vm stop "${vm}-vme"
    fi
  done
  success "All VMs stopped successfully!"
}

all_restart() {
  slog "Restarting all VMs..."
  for vm in "${VM_EASY_DISTRO_LIST[@]}"; do
    if vm_exists "${vm}-vme"; then
      slog "Restarting VM: ${vm}-vme"
      vm restart "${vm}-vme"
    fi
  done
  success "All VMs restarted successfully!"
}

usage() {
  all_usage "libvirt" "VMs"
}

main() {
  virt_check_prerequisites
  all_parse_args "$@"
}

main "$@"
