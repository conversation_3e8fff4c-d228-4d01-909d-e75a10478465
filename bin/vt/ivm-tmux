#!/usr/bin/env bash

# shellcheck disable=SC1091

# Support all virtual machines created by ivm-all

set -euo pipefail

source "$(dirname "$0")/ivm-utils"

if ! has_cmd ivm_exists; then
    slog FOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO
    exit 100
fi

source "$(dirname "$0")/tmux-utils"

IVM_DISTRO_LIST=("ubuntu" "fedora" "arch" "tw" "debian")

SESSION_NAME="IVM_TMUX"

usage() {
    cat <<EOF
Usage: $(basename "$0") [OPTION]

Manage a tmux session with SSH connections to Incus VMs.

Options:
  create    Create a new tmux session with SSH connections to VMs (default if no option)
  attach    Attach to an existing session
  detach    Detach from the current session
  destroy   Kill the tmux session
  help      Display this help message

Examples:
  $(basename "$0")           # Create session or attach if exists
  $(basename "$0") create    # Force create a new session
  $(basename "$0") attach    # Attach to existing session
  $(basename "$0") detach    # Detach from current session
  $(basename "$0") destroy   # Kill the session
EOF
}

create_session() {
    tmux_session "$SESSION_NAME" "$1"

    slog "Creating tmux session '$SESSION_NAME' with SSH connections to 6 Incus VMs..."

    local ssh_cmds=()
    create_commands ssh_cmds "ivm" "${IVM_DISTRO_LIST[@]}"

    start_sessions "ivm" "${IVM_DISTRO_LIST[@]}"

    if ! tmux_grid "$SESSION_NAME" "${ssh_cmds[@]}"; then
        fail "Failed to create tmux session '$SESSION_NAME' with SSH connections to VMs."
        return 1
    fi

    if ! tmux attach-session -t "$SESSION_NAME"; then
        fail "Failed to attach to tmux session '$SESSION_NAME'."
        return 1
    fi
}

main() {
    ivm_check_exists_prerequisites
    check_tmux

    local command="${1:-}"

    case "$command" in
    create | "" | -c | --create)
        create_session "true"
        ;;
    attach | -a | --attach)
        attach_session "$SESSION_NAME"
        ;;
    detach | -d | --detach)
        detach_session "$SESSION_NAME"
        ;;
    destroy | -x | --destroy)
        destroy_session "$SESSION_NAME"
        ;;
    help | --help | -h)
        usage
        ;;
    *)
        fail "Unknown option: $command"
        usage
        exit 1
        ;;
    esac
}

main "$@"
