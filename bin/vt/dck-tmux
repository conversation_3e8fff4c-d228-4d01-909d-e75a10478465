#!/usr/bin/env bash

set -euo pipefail

# shellcheck disable=SC1091
source "$(dirname "$0")/dck-utils"

SESSION_NAME="DCK_TMUX"

DCK_DISTRO_LIST=("ubuntu" "debian" "arch" "fedora" "rocky" "tw" "alpine" "centos" "nix")

usage() {
  cat <<EOF
Usage: $(basename "$0") [OPTION]

Manage a tmux session with connections to Docker containers.

Options:
  create    Create a new tmux session with connections to containers (default if no option)
  attach    Attach to an existing session
  detach    Detach from the current session
  destroy   Kill the tmux session
  help      Display this help message
EOF
}

check_docker() {
  has_cmd docker || err_exit "docker is not installed. Please install it first."
}

dck_exists() {
  local container_name="$1"
  docker ps -a --format "{{.Names}}" | grep -q "^$container_name$"
  return $?
}

dck_check_exists() {
  local container_name="$1"
  if ! dck_exists "$container_name"; then
    fail "Container '$container_name' does not exist"
    return 1
  fi
  return 0
}

dck_state() {
  local container_name="$1"
  docker inspect "$container_name" --format "{{.State.Status}}" 2>/dev/null || true
}

is_dck_running() {
  local container_name="$1"
  docker ps --format "{{.Names}}" | grep -q "^$container_name$"
}

check_dck_running() {
  local container_name="$1"
  if ! is_dck_running "$container_name"; then
    fail "Container '$container_name' is not running"
    return 1
  fi
  return 0
}

create_session() {
  tmux_session "$SESSION_NAME" "$1"

  slog "Creating tmux session '$SESSION_NAME' with connections to Docker containers..."

  local ssh_cmds=()
  create_commands ssh_cmds "dck" "${DCK_DISTRO_LIST[@]}"

  start_sessions "dck" "${DCK_DISTRO_LIST[@]}"

  if ! tmux_grid "$SESSION_NAME" "${ssh_cmds[@]}"; then
    fail "Failed to create tmux session '$SESSION_NAME' with connections to containers."
    return 1
  fi

  if ! tmux attach-session -t "$SESSION_NAME"; then
    fail "Failed to attach to tmux session '$SESSION_NAME'."
    return 1
  fi
}

attach_session() {
  if ! tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    warn "Session '$SESSION_NAME' does not exist. Creating it..."
    create_session
    return
  fi

  slog "Attaching to session '$SESSION_NAME'..."
  tmux attach-session -t "$SESSION_NAME"
}

detach_session() {
  [[ -z "${TMUX:-}" ]] || err_exit "Not currently in a tmux session"

  slog "Detaching from tmux session..."
  tmux detach-client
}

destroy_session() {
  if ! tmux has-session -t "$SESSION_NAME" 2>/dev/null; then
    warn "Session '$SESSION_NAME' does not exist"
    return
  fi

  slog "Destroying session '$SESSION_NAME'..."
  tmux kill-session -t "$SESSION_NAME"
  success "Session destroyed"
}

main() {
  check_docker
  check_tmux

  local command="${1:-}"

  case "$command" in
  create)
    create_session "true"
    ;;
  attach)
    attach_session
    ;;
  detach)
    detach_session
    ;;
  destroy)
    destroy_session
    ;;
  help | --help | -h)
    usage
    ;;
  "")
    create_session
    ;;
  *)
    fail "Unknown option: $command"
    usage
    exit 1
    ;;
  esac
}

main "$@"
