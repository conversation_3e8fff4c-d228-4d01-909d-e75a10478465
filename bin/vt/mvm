#!/usr/bin/env bash

set -euo pipefail

# shellcheck disable=SC1091
source "$(dirname "$0")/vt-utils"

usage() {
    cat <<EOF
Usage: $0 <command> [vm-name] [args...]

Manage Multipass virtual machines similar to libvirt VM management.

COMMANDS:
    install                 Install Multipass using ilmi
    list                    List all Multipass VMs
    status <name>           Show VM status and info
create <distro> [name] [options]  Create a new Multipass VM
    start <name>            Start a VM
    stop <name>             Stop a VM
    restart <name>          Restart a VM
    delete <name>           Delete a VM completely
    shell <name>            Connect to VM shell
    exec <name> <cmd>       Execute command in VM
    ip <name>               Get VM IP address
    ssh <name> [username]   Connect to VM via SSH
    info <name>             Show detailed VM information
    mount <name> <src> <dst> Mount host directory in VM
    umount <name> <path>    Unmount directory from VM

CREATE OPTIONS:
    --vcpus N, --cpu N      Number of CPUs (default: 2)
    --memory XG             Memory size (default: 4G)
    --disk-size XG          Disk size (default: 20G)
    --nix                   Install Nix using Determinate Systems installer

SUPPORTED DISTRIBUTIONS:
    ubuntu                  Ubuntu (latest LTS)
    lts                     Ubuntu (latest LTS alias)
    jammy                   Ubuntu 22.04 LTS
    noble                   Ubuntu 24.04 LTS
    oracular                Ubuntu 24.10
    plucky                  Ubuntu 25.04

EXAMPLES:
    $0 install                      # Install Multipass
    $0 list                         # List all VMs
    $0 status ubuntu-vm             # Show status of 'ubuntu-vm'
    $0 create ubuntu myubuntu       # Create Ubuntu VM named 'myubuntu'
    $0 create ubuntu --vcpus 4 --memory 8G --disk-size 40G  # Create with custom specs
    $0 create ubuntu --nix          # Create Ubuntu VM with Nix pre-installed
    $0 create oracular              # Create Ubuntu 24.10 VM with default name
    $0 create jammy my-jammy --vcpus 1 --memory 2G        # Create with custom CPU/memory
    $0 shell ubuntu-vm              # Connect to VM shell
    $0 exec ubuntu-vm "ls -la"      # Run command in VM
    $0 ip ubuntu-vm                 # Get IP address of 'ubuntu-vm'
    $0 ssh ubuntu-vm                # SSH to 'ubuntu-vm' (auto-detect username)
    $0 ssh ubuntu-vm ubuntu         # SSH to 'ubuntu-vm' as 'ubuntu' user
    $0 mount ubuntu-vm ~/code /home/<USER>/code  # Mount host directory
    $0 delete old-vm                # Delete 'old-vm' completely

EOF
}

check_multipass() {
    if ! command -v multipass >/dev/null 2>&1; then
        fail "Multipass is not installed or not in PATH"
        slog "Install it with: $0 install"
        return 1
    fi
    return 0
}

multipass_list_vms() {
    local vm_name="${1:-}"
    if [[ -n "$vm_name" ]]; then
        multipass list | grep -E "(Name|$vm_name)" || true
    else
        multipass list
    fi
}

install_multipass() {
    slog "Installing Multipass using ilmi..."

    # Check if ilmi is available
    if has_cmd ilmi; then
        ilmi multipass
    else
        fail "ilmi not found. Installing it first..."
    fi

    if has_cmd multipass; then
        success "Multipass installed successfully!"
        echo
        slog "You can now use:"
        slog "  $0 list                    # List VMs"
        slog "  $0 create ubuntu           # Create Ubuntu VM"
    else
        fail "Multipass installation failed. Please check the output above for errors."
        return 1
    fi
}

list_vms() {
    check_multipass || return 1

    slog "Listing all Multipass VMs..."
    echo
    multipass_list_vms
}

vm_status() {
    local vm_name="$1"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    slog "Status for VM '$vm_name':"
    echo
    multipass_list_vms "$vm_name"
    echo

    slog "Detailed information:"
    multipass info "$vm_name"
}

detect_username_from_vm_name() {
    local vm_name="$1"

    # Multipass primarily supports Ubuntu, so default to ubuntu user
    # This function is kept for consistency with other VM scripts
    echo "ubuntu"
}

create_vm() {
    local distro="$1"
    shift

    # Default values
    local vm_name=""
    local cpus="2"
    local memory="4G"
    local disk_size="20G"
    local install_nix=false

    local ssh_pubkey
    ssh_pubkey=$(ssh_key_path)

    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
        --vcpus | --cpu)
            cpus="$2"
            shift 2
            ;;
        --memory)
            memory="$2"
            shift 2
            ;;
        --disk-size)
            disk_size="$2"
            shift 2
            ;;
        --nix)
            install_nix=true
            shift
            ;;
        -*)
            fail "Unknown option: $1"
            return 1
            ;;
        *)
            # First non-option argument is the VM name
            if [[ -z "$vm_name" ]]; then
                vm_name="$1"
            else
                fail "Unexpected argument: $1"
                return 1
            fi
            shift
            ;;
        esac
    done

    # Set default VM name if not provided
    if [[ -z "$vm_name" ]]; then
        vm_name="${distro}-vm"
    fi

    check_multipass || return 1

    local nix_info=""
    if [[ "$install_nix" == "true" ]]; then
        nix_info=" with Nix"
    fi
    slog "Creating $distro Multipass VM: $vm_name (CPUs: $cpus, Memory: $memory, Disk: $disk_size)$nix_info"

    # Check if VM already exists
    if multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' already exists"
        return 1
    fi

    # Create cloud-init config for SSH setup in user's home directory (accessible to Multipass)
    local cloud_init_file
    cloud_init_file="$HOME/.multipass-cloud-init-$vm_name-$(date +%s).yaml"
    trap 'rm -f "$cloud_init_file"' EXIT

    local username="ubuntu" # Multipass primarily supports Ubuntu

    # Build cloud-init configuration
    local cloud_init_content
    cloud_init_content="#cloud-config
users:
  - name: $username
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    ssh-authorized-keys:
      - $(cat "$ssh_pubkey")"

    # Add Nix installation if requested
    if [[ "$install_nix" == "true" ]]; then
        cloud_init_content+="

packages:
  - curl
  - ca-certificates

runcmd:
  - |
    # Install Nix using Determinate Systems installer
    curl --proto '=https' --tlsv1.2 -sSf -L https://install.determinate.systems/nix | sh -s -- install --no-confirm
    # Source Nix for all users
    echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/bash.bashrc
    echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /etc/profile
    # Add Nix to PATH for the user
    echo 'source /nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' >> /home/<USER>/.bashrc
    # Enable nix-daemon
    systemctl enable --now nix-daemon || true

final_message: \"VM setup complete! Nix package manager is installed and ready to use.\""
    else
        cloud_init_content+="

final_message: \"VM setup complete!\""
    fi

    # Write the cloud-init configuration
    tee "$cloud_init_file" >/dev/null <<EOF
$cloud_init_content
EOF

    # Use distro name directly as image name - Multipass supports Ubuntu variants
    local image_name="$distro"

    # Validate supported distributions
    case "$distro" in
    ubuntu | lts | jammy | noble | oracular | plucky)
        # Valid Multipass Ubuntu images
        ;;
    *)
        fail "Unsupported distribution: $distro"
        slog "Supported distributions: ubuntu, lts, jammy, noble, oracular, plucky"
        rm -f "$cloud_init_file"
        return 1
        ;;
    esac

    # Launch VM
    slog "Launching VM with image: $image_name"
    if multipass launch \
        --name "$vm_name" \
        --cpus "$cpus" \
        --memory "$memory" \
        --disk "$disk_size" \
        --cloud-init "$cloud_init_file" \
        "$image_name"; then

        success "VM '$vm_name' created successfully!"

        # Wait for VM to be ready
        slog "Waiting for VM to be ready..."
        sleep 10

        # Show VM info
        multipass_list_vms "$vm_name"

        # Show connection info
        echo
        slog "VM is ready! You can connect using:"
        slog "  $0 shell $vm_name           # Direct shell access"
        slog "  $0 ssh $vm_name             # SSH access"
        slog "  $0 ip $vm_name              # Get IP address"
    else
        fail "Failed to create VM '$vm_name'"
        rm -f "$cloud_init_file"
        return 1
    fi

    rm -f "$cloud_init_file"
}

start_vm() {
    local vm_name="$1"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(multipass list --format csv | grep "^$vm_name," | cut -d',' -f2)

    if [[ "$state" == "Running" ]]; then
        warn "VM '$vm_name' is already running"
        return 0
    fi

    slog "Starting VM '$vm_name'..."
    if multipass start "$vm_name"; then
        success "VM '$vm_name' started"
        sleep 2
        slog "VM status:"
        multipass_list_vms "$vm_name"
    else
        fail "Failed to start VM '$vm_name'"
        return 1
    fi
}

stop_vm() {
    local vm_name="$1"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(multipass list --format csv | grep "^$vm_name," | cut -d',' -f2)

    if [[ "$state" == "Stopped" ]]; then
        warn "VM '$vm_name' is already stopped"
        return 0
    fi

    slog "Stopping VM '$vm_name'..."
    if multipass stop "$vm_name"; then
        success "VM '$vm_name' stopped"
        multipass_list_vms "$vm_name"
    else
        fail "Failed to stop VM '$vm_name'"
        return 1
    fi
}

restart_vm() {
    local vm_name="$1"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    slog "Restarting VM '$vm_name'..."
    if multipass restart "$vm_name"; then
        success "VM '$vm_name' restarted"
        sleep 2
        multipass_list_vms "$vm_name"
    else
        fail "Failed to restart VM '$vm_name'"
        return 1
    fi
}

delete_vm() {
    local vm_name="$1"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    warn "This will permanently delete VM '$vm_name' and all its data!"
    read -p "Are you sure? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        slog "Deletion cancelled"
        return 0
    fi

    slog "Deleting VM '$vm_name'..."
    if multipass delete "$vm_name" --purge; then
        success "VM '$vm_name' deleted successfully"
    else
        fail "Failed to delete VM '$vm_name'"
        return 1
    fi
}

connect_shell() {
    local vm_name="$1"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(multipass list --format csv | grep "^$vm_name," | cut -d',' -f2)

    if [[ "$state" != "Running" ]]; then
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    slog "Connecting to shell of VM '$vm_name'..."
    multipass shell "$vm_name"
}

exec_in_vm() {
    local vm_name="$1"
    shift
    local command="$*"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(multipass list --format csv | grep "^$vm_name," | cut -d',' -f2)

    if [[ "$state" != "Running" ]]; then
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    slog "Executing command in VM '$vm_name': $command"
    multipass exec "$vm_name" -- "$@"
}

get_vm_ip() {
    local vm_name="$1"

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        return 1
    fi

    local state
    state=$(multipass list --format csv | grep "^$vm_name," | cut -d',' -f2)

    if [[ "$state" != "Running" ]]; then
        return 2
    fi

    # Get IP from multipass info
    local ip
    ip=$(multipass info "$vm_name" | awk '/IPv4/ {print $2}' | head -1)

    if [[ -z "$ip" ]]; then
        return 3
    fi

    echo "$ip"
    return 0
}

show_ip() {
    local vm_name="$1"
    check_multipass || return 1

    if [[ -z "$vm_name" ]]; then
        fail "VM name required"
        return 1
    fi

    local ip
    ip=$(get_vm_ip "$vm_name")
    local ret=$?

    case $ret in
    1)
        fail "VM '$vm_name' not found"
        return 1
        ;;
    2)
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
        ;;
    3)
        fail "Could not determine IP address for VM '$vm_name'"
        slog "VM may still be starting up. Try again in a few moments."
        return 1
        ;;
    0)
        echo "$ip"
        return 0
        ;;
    esac
}

ssh_to_vm() {
    local vm_name="$1"
    local username="${2:-}"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    local state
    state=$(multipass list --format csv | grep "^$vm_name," | cut -d',' -f2)

    if [[ "$state" != "Running" ]]; then
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
    fi

    # Auto-detect username if not provided
    if [[ -z "$username" ]]; then
        username=$(detect_username_from_vm_name "$vm_name")
        slog "Auto-detected username: $username"
    fi

    local ip
    ip=$(get_vm_ip "$vm_name")
    local ret=$?

    case $ret in
    1)
        fail "VM '$vm_name' not found"
        return 1
        ;;
    2)
        fail "VM '$vm_name' is not running"
        slog "Start it with: $0 start $vm_name"
        return 1
        ;;
    3)
        fail "Could not determine IP address for VM '$vm_name'"
        slog "VM may still be starting up. Try again in a few moments."
        return 1
        ;;
    0)
        slog "Connecting to $vm_name ($ip) as $username..."
        ssh "$username@$ip"
        ;;
    esac
}

show_vm_info() {
    local vm_name="$1"
    check_multipass || return 1

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    slog "Detailed information for VM '$vm_name':"
    echo
    multipass info "$vm_name"
}

mount_directory() {
    local vm_name="$1"
    local source_path="$2"
    local target_path="$3"
    check_multipass || return 1

    if [[ -z "$vm_name" || -z "$source_path" || -z "$target_path" ]]; then
        fail "Usage: $0 mount <vm-name> <source-path> <target-path>"
        return 1
    fi

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    if [[ ! -d "$source_path" ]]; then
        fail "Source directory '$source_path' does not exist"
        return 1
    fi

    slog "Mounting '$source_path' to '$target_path' in VM '$vm_name'..."
    if multipass mount "$source_path" "$vm_name:$target_path"; then
        success "Directory mounted successfully"
        slog "You can access the files at '$target_path' in the VM"
    else
        fail "Failed to mount directory"
        return 1
    fi
}

umount_directory() {
    local vm_name="$1"
    local mount_path="$2"
    check_multipass || return 1

    if [[ -z "$vm_name" || -z "$mount_path" ]]; then
        fail "Usage: $0 umount <vm-name> <mount-path>"
        return 1
    fi

    if ! multipass info "$vm_name" >/dev/null 2>&1; then
        fail "VM '$vm_name' not found"
        return 1
    fi

    slog "Unmounting '$mount_path' from VM '$vm_name'..."
    if multipass umount "$vm_name:$mount_path"; then
        success "Directory unmounted successfully"
    else
        fail "Failed to unmount directory"
        return 1
    fi
}

# Main command handling
if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

command="$1"
vm_name="${2:-}"

case "$command" in
install)
    install_multipass
    ;;
list)
    list_vms
    ;;
status)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    vm_status "$vm_name"
    ;;
create)
    [[ -z "$vm_name" ]] && {
        fail "Distribution name required"
        usage
        exit 1
    }
    distro="$vm_name"
    # Pass all remaining arguments to create_vm
    create_vm "$distro" "${@:3}"
    ;;
start)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    start_vm "$vm_name"
    ;;
stop)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    stop_vm "$vm_name"
    ;;
restart)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    restart_vm "$vm_name"
    ;;
delete)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    delete_vm "$vm_name"
    ;;
shell)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    connect_shell "$vm_name"
    ;;
exec)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    [[ $# -lt 3 ]] && {
        fail "Command required"
        usage
        exit 1
    }
    exec_in_vm "$vm_name" "${@:3}"
    ;;
ip)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    show_ip "$vm_name"
    ;;
ssh)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    ssh_to_vm "$vm_name" "${3:-}"
    ;;
info)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    show_vm_info "$vm_name"
    ;;
mount)
    [[ $# -lt 4 ]] && {
        fail "Usage: $0 mount <vm-name> <source-path> <target-path>"
        usage
        exit 1
    }
    mount_directory "$vm_name" "$3" "$4"
    ;;
umount)
    [[ -z "$vm_name" ]] && {
        fail "VM name required"
        usage
        exit 1
    }
    [[ -z "$3" ]] && {
        fail "Mount path required"
        usage
        exit 1
    }
    umount_directory "$vm_name" "$3"
    ;;
*)
    fail "Unknown command: $command"
    usage
    exit 1
    ;;
esac
