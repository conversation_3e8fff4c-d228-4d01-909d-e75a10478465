#!/usr/bin/env bash
set -euo pipefail

CONF="/etc/libvirt/network.conf"
LINE='firewall_backend = "iptables"'
USE_SUDO=""

# Parse options
while [[ $# -gt 0 ]]; do
  case "$1" in
  --restore)
    ACTION="restore"
    shift
    ;;
  --restore-file)
    ACTION="restore-file"
    RESTORE_FILE="${2:-}"
    shift 2
    ;;
  --list-backups)
    ACTION="list"
    shift
    ;;
  --sudo)
    USE_SUDO="sudo"
    shift
    ;;
  *)
    echo "Unknown option: $1"
    echo "Usage: $0 [--sudo] [--restore|--restore-file <file>|--list-backups]"
    exit 1
    ;;
  esac
done

# Function to run commands with/without sudo
run() {
  $USE_SUDO "$@"
}

# Backup before modifying
backup() {
  local BACKUP
  BACKUP="${CONF}.$(date +%Y%m%d%H%M%S).bak"
  echo "Creating backup at $BACKUP"
  run cp "$CONF" "$BACKUP"
}

# Restore latest backup
restore_latest() {
  local LAST_BACKUP
  LAST_BACKUP=$(find /etc/libvirt -maxdepth 1 -type f -name "network.conf.*.bak" -printf "%T@ %p\n" |
    sort -nr |
    awk 'NR==1{print $2}')

  if [ -z "$LAST_BACKUP" ]; then
    echo "No backup found to restore."
    exit 1
  fi

  echo "Restoring from $LAST_BACKUP..."
  run cp "$LAST_BACKUP" "$CONF"
  echo "Restore complete."
}

# List all backups
list_backups() {
  local FILES
  FILES=$(find /etc/libvirt -maxdepth 1 -type f -name "network.conf.*.bak" | sort -r || true)

  if [ -z "$FILES" ]; then
    echo "No backups found."
    exit 0
  fi

  echo "Available backups:"
  echo "$FILES"
}

# Restore a specific backup file
restore_file() {
  local FILE="$1"

  if [ ! -f "$FILE" ]; then
    echo "Backup file not found: $FILE"
    exit 1
  fi

  echo "Restoring from $FILE..."
  run cp "$FILE" "$CONF"
  echo "Restore complete."
}

# Handle actions
case "${ACTION:-enforce}" in
restore)
  restore_latest
  exit 0
  ;;
restore-file)
  if [ -z "${RESTORE_FILE:-}" ]; then
    echo "Usage: $0 --restore-file <backup-file>"
    exit 1
  fi
  restore_file "$RESTORE_FILE"
  exit 0
  ;;
list)
  list_backups
  exit 0
  ;;
esac

# Default action = enforce
if run grep -qE "^\s*${LINE}" "$CONF"; then
  echo "Already set: $LINE"
  exit 0
fi

if run grep -qE "^\s*#\s*${LINE}" "$CONF"; then
  backup
  echo "Uncommenting firewall_backend..."
  run sed -i "s|^\s*#\s*${LINE}|${LINE}|" "$CONF"
  echo "Done."
  exit 0
fi

backup
echo "Appending missing line..."
echo "$LINE" | run tee -a "$CONF" >/dev/null
echo "Done."
