#!/usr/bin/env bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}
# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

VCPUS=4
RAM_MB=8192
SSH_PUBKEY="${SSH_PUBKEY:-$(cat ~/.ssh/id_ed25519.pub)}"
VM_NAME="flatcar-demo"
VM_DIR="${HOME}/vms/${VM_NAME}"

IMAGE_URL="https://stable.release.flatcar-linux.net/amd64-usr/current/flatcar_production_qemu_image.img"
IMAGE_RAW="${VM_DIR}/flatcar_production_qemu_image.img"
DISK="${VM_DIR}/${VM_NAME}.qcow2"
IGNITION_FILE="${VM_DIR}/config.json"

usage() {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  --name NAME         VM name (default: flatcar-demo)"
  echo "  --dir DIR           VM directory (default: ${HOME}/vms/NAME)"
  echo "  --vcpus VCPUS       Number of vCPUs (default: 4)"
  echo "  --memory MB         RAM in MB (default: 8192)"
  echo "  --ssh-key PATH      SSH public key path (default: auto-detect)"
  echo "  --help, -h          Show this help"
}

parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
    --name)
      VM_NAME="$2"
      shift 2
      ;;
    --dir)
      VM_DIR="$2"
      shift 2
      ;;
    --vcpus)
      VCPUS="$2"
      shift 2
      ;;
    --memory)
      RAM_MB="$2"
      shift 2
      ;;
    --ssh-key)
      SSH_PUBKEY=$(<"$2")
      shift 2
      ;;
    --help | -h)
      usage
      exit 0
      ;;
    *)
      fail "Unknown option: $1"
      usage
      exit 1
      ;;
    esac
  done
}

check_prerequisites() {
  for cmd in curl jq qemu-system-x86_64 qemu-img; do
    has_cmd "$cmd" || err_exit "Install $cmd first"
  done
}

download_flatcar_image() {
  mkdir -p "${VM_DIR}"

  if [[ ! -f "${IMAGE_RAW}" ]]; then
    echo "Downloading Flatcar QEMU image …"
    curl -L "${IMAGE_URL}" -o "${IMAGE_RAW}"
  fi
}

create_disk_image() {
  if [[ ! -f "${DISK}" ]]; then
    echo "Creating ${DISK} from raw image …"
    qemu-img convert -f raw -O qcow2 "${IMAGE_RAW}" "${DISK}"
  fi
}

create_ignition() {
  cat >"${IGNITION_FILE}" <<EOF
{
  "ignition": { "version": "3.4.0" },
  "passwd": {
    "users": [
      {
        "name": "core",
        "sshAuthorizedKeys": ["${SSH_PUBKEY}"],
        "groups": ["sudo", "docker"]
      }
    ]
  },
  "systemd": {
    "units": [
      {
        "name": "qemu-guest-agent.service",
        "enabled": true
      }
    ]
  }
}
EOF
}

launch_vm() {
  echo "Starting ${VM_NAME} …"
  exec qemu-system-x86_64 \
    -name "${VM_NAME}" \
    -m "${RAM_MB}" \
    -smp "${VCPUS}" \
    -cpu host \
    -enable-kvm \
    -machine q35,accel=kvm \
    -netdev user,id=net0,hostfwd=tcp::2223-:22 \
    -device virtio-net-pci,netdev=net0 \
    -drive if=virtio,file="${DISK}",discard=unmap,detect-zeroes=unmap \
    -device virtio-rng-pci \
    -fw_cfg name=opt/com.coreos/config,file="${IGNITION_FILE}" \
    -nographic
}

main() {
  parse_args "$@"
  check_prerequisites
  download_flatcar_image
  create_disk_image
  create_ignition
  launch_vm
}

main "$@"
