#!/usr/bin/env bash

set -euo pipefail

# shellcheck disable=SC1091
source "$(dirname "$0")/vm-utils"

unset USERNAME
unset PASSWORD

VCPUS="4"
MEMORY="8192"
DISK_SIZE="30G"
BRIDGE="default"
OS_VARIANT="linux"

check_whiptail() {
  if ! has_cmd whiptail; then
    echo "Error: whiptail is not installed. Please install it to use interactive mode." >&2
    exit 1
  fi
}

setup_storage() {
  local base_dir IMG_DIR

  base_dir="/var/lib/libvirt/images"

  IMG_DIR="${base_dir}/base-images"
  WORK_DIR="${base_dir}/${VM_NAME}" # global

  sudo mkdir -p "$IMG_DIR" "$WORK_DIR"

  sudo chown -R "$USER":kvm "$IMG_DIR"
  sudo chmod -R 755 "$IMG_DIR"

  sudo chown -R "$USER":kvm "$WORK_DIR"
  sudo chmod -R 755 "$WORK_DIR"

  sudo mkdir -p /var/lib/libvirt/boot # bug in libvirt/virt-install?

  BASE_IMAGE="${IMG_DIR}/${BASE_IMAGE_NAME}"
  VM_DISK="${WORK_DIR}/${VM_NAME}.qcow2"
}

cleanup_on_error() {
  warn "Cleaning up due to error..."

  if [[ -d "$WORK_DIR" ]]; then
    sudo rm -rf "$WORK_DIR" 2>/dev/null
  fi

  stop_sudo_keepalive
}

trap cleanup_on_error ERR
trap 'cleanup_on_error; exit 1' INT TERM

configure_arch() {
  VM_NAME=${VM_NAME:-"arch-vme"}
  RELEASE=${RELEASE:="latest"}
  BASE_IMAGE_NAME="Arch-Linux-x86_64-cloudimg.qcow2"
  DOWNLOAD_URL="https://geo.mirror.pkgbuild.com/images/latest/${BASE_IMAGE_NAME}"
  USER_GROUPS=("wheel" "network" "storage")
  OS_VARIANT="archlinux"
}

configure_ubuntu() {
  VM_NAME=${VM_NAME:-"ubuntu-vme"}
  RELEASE=${RELEASE:="plucky"}
  BASE_IMAGE_NAME="${RELEASE}-server-cloudimg-amd64.img"
  DOWNLOAD_URL="https://cloud-images.ubuntu.com/${RELEASE}/current/${BASE_IMAGE_NAME}"
  USER_GROUPS=("sudo" "adm" "sambashare")
  OS_VARIANT="ubuntu25.04"
}

configure_debian() {
  VM_NAME=${VM_NAME:="debian-vme"}
  RELEASE=${RELEASE:="trixie"} # 13
  BASE_IMAGE_NAME="debian-13-generic-amd64.qcow2"
  DOWNLOAD_URL="https://cloud.debian.org/images/cloud/trixie/latest/${BASE_IMAGE_NAME}"
  USER_GROUPS=("sudo" "adm")
  OS_VARIANT="debian13"
}

configure_fedora() {
  VM_NAME=${VM_NAME:="fedora-vme"}
  RELEASE=${RELEASE:=42}
  BASE_IMAGE_NAME="Fedora-Cloud-Base-Generic-${RELEASE}-1.1.x86_64.qcow2"
  DOWNLOAD_URL="https://download.fedoraproject.org/pub/fedora/linux/releases/${RELEASE}/Cloud/x86_64/images/${BASE_IMAGE_NAME}"
  USER_GROUPS=("wheel" "network" "storage")
  OS_VARIANT="fedora42"
}

configure_tw() {
  VM_NAME=${VM_NAME:-tw-vme}
  RELEASE=${RELEASE:=latest}
  BASE_IMAGE_NAME="openSUSE-Tumbleweed-Minimal-VM.x86_64-Cloud.qcow2"
  DOWNLOAD_URL="https://download.opensuse.org/tumbleweed/appliances/${BASE_IMAGE_NAME}"
  USER_GROUPS=("wheel" "network" "users")
  OS_VARIANT="opensusetumbleweed"
}

configure_distribution() {
  case "$DISTRO" in
  arch)
    configure_arch
    ;;
  ubuntu)
    configure_ubuntu
    ;;
  debian)
    configure_debian
    ;;
  fedora)
    configure_fedora
    ;;
  tw | tumbleweed)
    configure_tw
    ;;
  *)
    echo "Unknown distribution: $DISTRO"
    usage
    exit 1
    ;;
  esac

  USERNAME=${USERNAME:-"$DISTRO"}
  PASSWORD=${PASSWORD:=$USERNAME}
  PASSWORD_HASH=$(openssl passwd -6 "$PASSWORD")
}

download_base_image() {
  if [ -f "${BASE_IMAGE}" ]; then
    echo "Base image '${BASE_IMAGE}' already exists. Skipping download."
    return 0
  fi

  echo "Downloading base image from $DOWNLOAD_URL..."
  if ! sudo wget -q --show-progress "$DOWNLOAD_URL" -O "${BASE_IMAGE}"; then
    echo "Error: Failed to download base image." >&2
  else
    sudo chown root:kvm "${BASE_IMAGE}"
    sudo chmod 644 "${BASE_IMAGE}"
    echo "Base image downloaded successfully."
  fi
}

generate_cloud_init() {
  slog "Generating cloud-init configuration..."

  local cloud_init_dir
  cloud_init_dir=$(mktemp -d)

  USER_DATA="${cloud_init_dir}/user-data"
  META_DATA="${cloud_init_dir}/meta-data"

  local pub_key
  pub_key=$(cat "$SSH_KEY")

  local -a packages=(
    "qemu-guest-agent"
    "curl"
    "openssh-server"
  )

  cat >"${USER_DATA}" <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

users:
  - name: $USERNAME
    groups:
$(printf "      - %s\n" "${USER_GROUPS[@]}")
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - "$pub_key"

package_update: true
packages:
$(printf "  - %s\n" "${packages[@]}")

ssh_pwauth: false
disable_root: true

runcmd:
  - systemctl enable --now sshd || true

  - systemctl enable --now qemu-guest-agent || true
EOF

  cat >"${META_DATA}" <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

  success "Cloud-init configuration generated"
}

check_prerequisites() {
  if ! groups "$USER" | grep -q '\blibvirtd\?\b'; then
    fail "Error: User '$USER' is not in the 'libvirt' group." >&2
    fail "Please run: 'sudo usermod -aG libvirt \$USER' and then log out and back in." >&2
    exit 1
  fi

  if virsh dominfo "${VM_NAME}" &>/dev/null; then
    fail "Error: A VM named '${VM_NAME}' already exists." >&2
    fail "Please remove it with: virsh destroy ${VM_NAME} && virsh undefine ${VM_NAME}" >&2
    exit 1
  fi

  if [ -f "${VM_DISK}" ]; then
    fail "Error: A disk file at '${VM_DISK}' already exists." >&2
    fail "Please remove it with: sudo rm -f ${VM_DISK}" >&2
    exit 1
  fi
}

create_disk() {
  echo "Creating and resizing disk for ${VM_NAME} at ${VM_DISK}..."
  sudo qemu-img create -f qcow2 -b "${BASE_IMAGE}" -F qcow2 "${VM_DISK}" "${DISK_SIZE}"
  echo "Disk created successfully."
}

create_vm() {
  echo "Creating VM '${VM_NAME}'..."

  virt-install \
    --connect qemu:///system \
    --name "${VM_NAME}" \
    --memory "${MEMORY}" \
    --os-variant "${OS_VARIANT}" \
    --vcpus "${VCPUS}" \
    --import \
    --disk path="${VM_DISK}",device=disk,bus=virtio,format=qcow2 \
    --network network="${BRIDGE}",model=virtio \
    --cloud-init user-data="${USER_DATA}",meta-data="${META_DATA}" \
    --video=virtio \
    --graphics none \
    --console pty,target_type=serial \
    --noautoconsole

  echo "✅ VM creation process started for '${VM_NAME}'."
}

show_completion_info() {
  echo "✅ VM should be up in few minutes. You can check the status with:"
  echo "  virsh domstate ${VM_NAME}"
  echo
  echo "Once the VM is up, you can get IP address with:"
  echo "  virsh domifaddr ${VM_NAME} --source agent"
  echo
  echo "Once you get IP from the above command, you can connect to it with:"
  echo "  ssh ${USERNAME}@<IP_ADDRESS>"
  echo
  echo "Provide password if prompted: ${PASSWORD}."
  echo
  echo "To troubleshoot, access the serial console with:"
  echo "  virsh console ${VM_NAME}"
  echo " You need to exit the console with Ctrl+]."
  echo
}

usage() {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  --username USERNAME   Set the username for the VM (default: 'arch')"
  echo "  --password PASSWORD   Set the password for the VM (default: same as username)"
  echo "  --vcpus VCPUS         Set the number of vCPUs (default: 4)"
  echo "  --memory MEMORY       Set the amount of memory in MB (default: 8192)"
  echo "  --distro DISTRO       Set the distribution (arch or ubuntu, default: arch)"
  echo "  --name VM_NAME        Set the name of the VM (default: 'arch')"
  echo "  --release RELEASE     Set the release version (default: 'latest')"
  echo "  --bridge BRIDGE       Set the network bridge (default: 'default')"
  echo "  --disk-size SIZE      Set the disk size (default: '30G')"
  echo "  --help                Show this help message"
}

parse_args() {
  while [[ $# -gt 0 ]]; do
    case "$1" in
    --distro)
      DISTRO="$2"
      shift 2
      ;;
    --name)
      VM_NAME="$2"
      shift 2
      ;;
    --release)
      RELEASE="$2"
      shift 2
      ;;
    --username)
      USERNAME="$2"
      shift 2
      ;;
    --password)
      PASSWORD="$2"
      shift 2
      ;;
    --vcpus)
      VCPUS="$2"
      shift 2
      ;;
    --memory)
      MEMORY="$2"
      shift 2
      ;;
    --disk-size)
      DISK_SIZE="$2"
      shift 2
      ;;
    --bridge)
      BRIDGE="$2"
      shift 2
      ;;
    --help)
      usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      usage
      exit 1
      ;;
    esac
  done
}

main() {
  virt_check_prerequisites

  if [ $# -eq 0 ]; then
    check_whiptail
    interactive_mode
  else
    parse_args "$@"
  fi

  configure_distribution
  setup_storage
  SSH_KEY=$(ssh_key_path)

  check_prerequisites

  slog "Starting VM creation process for '${VM_NAME}'..."
  echo

  echo "Using vCPUs: ${VCPUS}"
  echo "Using memory: ${MEMORY} MB"
  echo "Using disk size: ${DISK_SIZE}"
  echo
  sleep 1

  slog "Using username: ${USERNAME}"
  if [ -z "$PASSWORD" ]; then
    read -r -s -p "Enter password to use for the user: " PASSWORD
    echo
  fi

  download_base_image
  generate_cloud_init
  create_disk
  create_vm
  show_completion_info
}

interactive_mode() {
  if ! DISTRO=$(whiptail --title "Select Distribution" --menu "Choose a distribution" 15 60 5 \
    "arch" "Arch Linux" \
    "ubuntu" "Ubuntu" \
    "debian" "Debian" \
    "fedora" "Fedora" \
    "tumbleweed" "openSUSE Tumbleweed" 3>&1 1>&2 2>&3); then

    exit 1
  fi

  while true; do
    if ! VM_NAME=$(whiptail --title "VM Name" --inputbox "Enter the VM name" 10 60 "${DISTRO}-vme" 3>&1 1>&2 2>&3); then
      exit 1
    fi
    if [ -z "$VM_NAME" ]; then
      whiptail --title "Invalid Input" --msgbox "VM name cannot be empty." 8 78
    else
      break
    fi
  done

  while true; do
    if ! USERNAME=$(whiptail --title "Username" --inputbox "Enter the username" 10 60 "${DISTRO}" 3>&1 1>&2 2>&3); then
      exit 1
    fi
    if [ -z "$USERNAME" ]; then
      whiptail --title "Invalid Input" --msgbox "Username cannot be empty." 8 78
    else
      break
    fi
  done

  while true; do
    if ! PASSWORD=$(whiptail --title "Password" --passwordbox "Enter the password" 10 60 3>&1 1>&2 2>&3); then
      exit 1
    fi
    if [ -z "$PASSWORD" ]; then
      whiptail --title "Invalid Input" --msgbox "Password cannot be empty." 8 78
      continue
    fi

    if ! CONFIRM_PASSWORD=$(whiptail --title "Confirm Password" --passwordbox "Confirm the password" 10 60 3>&1 1>&2 2>&3); then
      exit 1
    fi

    if [ "$PASSWORD" != "$CONFIRM_PASSWORD" ]; then
      whiptail --title "Error" --msgbox "Passwords do not match. Please try again." 8 78
    else
      break
    fi
  done

  while true; do
    if ! VCPUS=$(whiptail --title "vCPUs" --inputbox "Enter the number of vCPUs" 10 60 "4" 3>&1 1>&2 2>&3); then
      exit 1
    fi
    if ! [[ "$VCPUS" =~ ^[0-9]+$ ]]; then
      whiptail --title "Invalid Input" --msgbox "vCPUs must be a number." 8 78
    else
      break
    fi
  done

  while true; do
    if ! MEMORY=$(whiptail --title "Memory" --inputbox "Enter the memory in MB" 10 60 "8192" 3>&1 1>&2 2>&3); then
      exit 1
    fi
    if ! [[ "$MEMORY" =~ ^[0-9]+$ ]]; then
      whiptail --title "Invalid Input" --msgbox "Memory must be a number." 8 78
    else
      break
    fi
  done

  while true; do
    if ! DISK_SIZE=$(whiptail --title "Disk Size" --inputbox "Enter the disk size (e.g., 30G)" 10 60 "30G" 3>&1 1>&2 2>&3); then
      exit 1
    fi
    if ! [[ "$DISK_SIZE" =~ ^[0-9]+[GM]$ ]]; then
      whiptail --title "Invalid Input" --msgbox "Disk size must end with G or M (e.g., 30G, 512M)." 8 78
    else
      break
    fi
  done

  while true; do
    if ! BRIDGE=$(whiptail --title "Bridge" --inputbox "Enter the bridge name" 10 60 "default" 3>&1 1>&2 2>&3); then
      exit 1
    fi
    if ! virsh net-list --all --name | grep -q "^$BRIDGE$"; then
      whiptail --title "Invalid Input" --msgbox "Bridge '$BRIDGE' not found." 8 78
    else
      break
    fi
  done

  whiptail --title "Summary" --msgbox "
  Distribution: $DISTRO
  VM Name:   $VM_NAME
  Username:  $USERNAME
  vCPUs:     $VCPUS
  Memory:    $MEMORY MB
  Disk Size: $DISK_SIZE
  " 15 60
}

main "$@"
