#!/usr/bin/env bash

virt-dev() {
  if vm_exists "dev"; then
    slog "Ubuntu VM already exists, skipping..."
  else
    slog "Creating Ubuntu VM: dev"
    vm-create --distro ubuntu --name dev --docker --brew --dotfiles min
  fi
}

virt-incus() {
  if vm_exists "incus"; then
    slog "Alpine VM already exists, skipping..."
  else
    slog "Creating Alpine VM: incus"
    vm-create --distro alpine --name incus --dotfiles min incus
  fi
}

virt-nix() {
  if vm_exists "nix"; then
    slog "Debian VM already exists, skipping..."
  else
    slog "Creating Debian VM: nix"
    vm-create --distro debian --name nix --nix
  fi
}

usage() {
  cat <<EOF
Usage: $(basename "$0") [OPTION]

Manage a tmux session with SSH connections to libvirt VMs.

Options:
  create    Create all VMs (default if no option)
  delete    Delete all VMs
  start     Start all VMs
  stop      Stop all VMs
  restart   Restart all VMs
  help      Display this help message
EOF
}

main() {
  if [[ $# -eq 0 ]]; then
    virt-dev
    virt-incus
    virt-nix
    exit 0
  fi

  if [[ "$1" == "delete" ]]; then
    vm delete dev
    vm delete incus
    vm delete nix
  elif [[ "$1" == "start" ]]; then
    vm start dev
    vm start incus
    vm start nix
  elif [[ "$1" == "stop" ]]; then
    vm stop dev
    vm stop incus
    vm stop nix
  elif [[ "$1" == "restart" ]]; then
    vm restart dev
    vm restart incus
    vm restart nix
  elif [[ "$1" == "--help" || "$1" == "help" || "$1" == "help" ]]; then
    usage
    exit 0
  else
    fail "Unknown option: $1"
    usage
    exit 1
  fi
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi
