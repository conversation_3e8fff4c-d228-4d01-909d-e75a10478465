#!/usr/bin/env bash

set -euo pipefail

# shellcheck disable=SC1091
source "$(dirname "$0")/vt-utils"

# Default values
DEFAULT_USERNAME="nixos"
DEFAULT_PASSWORD="nixos"
DEFAULT_SSH_KEY=""
CONTAINER_NAME=""
VM_NAME=""
SETUP_TYPE=""

usage() {
    cat <<EOF
Usage: $0 [OPTIONS] CONTAINER_OR_VM_NAME

Automatically configure NixOS containers/VMs with user setup, SSH access, and system configuration.

ARGUMENTS:
    CONTAINER_OR_VM_NAME    Name of the NixOS container or VM to configure

OPTIONS:
    --username USERNAME     Username to create (default: nixos)
    --password PASSWORD     Password for the user (default: nixos)
    --ssh-key PATH          Path to SSH public key (default: ~/.ssh/id_rsa.pub or ~/.ssh/id_ed25519.pub)
    --container             Configure as container (auto-detected if not specified)
    --vm                    Configure as VM (auto-detected if not specified)
    --help, -h              Show this help message

EXAMPLES:
    $0 my-nixos-container
    $0 --username admin --password secret123 my-nixos-vm
    $0 --ssh-key ~/.ssh/custom_key.pub nixos-dev

DESCRIPTION:
    This script automates the manual configuration of NixOS containers and VMs by:
    1. Creating a user with sudo access
    2. Setting up SSH access with key-based and password authentication
    3. Installing essential packages
    4. Applying a default NixOS configuration
    5. Enabling necessary services

EOF
}

detect_setup_type() {
    local name="$1"

    if incus info "$name" >/dev/null 2>&1; then
        local type
        type=$(incus info "$name" | grep "Type:" | awk '{print $2}')
        if [[ "$type" == "container" ]]; then
            SETUP_TYPE="container"
            CONTAINER_NAME="$name"
        elif [[ "$type" == "virtual-machine" ]]; then
            SETUP_TYPE="vm"
            VM_NAME="$name"
        else
            fail "Unknown Incus instance type: $type"
            exit 1
        fi
    else
        fail "Incus instance '$name' not found"
        exit 1
    fi
}

create_nixos_configuration() {
    local instance_name="$1"
    local username="$2"
    local ssh_key_content="$3"

    slog "Creating NixOS configuration.nix..."

    # Create the configuration.nix content
    local config_content
    read -r -d '' config_content <<EOF || true
# NixOS Configuration
# Generated by nixos-setup script

{ config, pkgs, ... }:

{
  # System packages
  environment.systemPackages = with pkgs; [
    # Essential tools
    curl
    wget
    git
    vim
    nano
    htop
    tree
    unzip
    tar

    # Network tools
    net-tools
    iproute2
    nmap

    # Development tools
    gcc
    gnumake

    # System utilities
    sudo
    openssh
    systemd
  ];

  # Enable essential services
  services.openssh = {
    enable = true;
    settings = {
      PasswordAuthentication = true;
      PermitRootLogin = "no";
      PubkeyAuthentication = true;
    };
  };

  # Network configuration
  networking = {
    hostName = "$instance_name";
    firewall = {
      enable = true;
      allowedTCPPorts = [ 22 ];  # SSH
    };
  };

  # User configuration
  users.users.$username = {
    isNormalUser = true;
    description = "Default NixOS user";
    extraGroups = [ "wheel" "networkmanager" "sudo" ];
    shell = pkgs.bash;
    password = "$DEFAULT_PASSWORD";  # This will be hashed automatically
$(if [[ -n "$ssh_key_content" ]]; then
        echo "    openssh.authorizedKeys.keys = ["
        echo "      \"$ssh_key_content\""
        echo "    ];"
    fi)
  };

  # Enable sudo for wheel group
  security.sudo = {
    enable = true;
    wheelNeedsPassword = false;  # Allow passwordless sudo for wheel group
  };

  # System configuration
  system.stateVersion = "24.11";

  # Enable nix flakes and new command
  nix.settings.experimental-features = [ "nix-command" "flakes" ];

  # Allow unfree packages
  nixpkgs.config.allowUnfree = true;

  # Time zone
  time.timeZone = "UTC";

  # Locale
  i18n.defaultLocale = "en_US.UTF-8";

  # Enable systemd services
  systemd.services.sshd.enable = true;
}
EOF

    # Write configuration to temporary file
    local temp_config
    temp_config=$(mktemp)
    echo "$config_content" >"$temp_config"

    # Copy configuration to the instance
    slog "Copying configuration.nix to $instance_name..."
    incus file push "$temp_config" "$instance_name/etc/nixos/configuration.nix"

    # Clean up
    rm -f "$temp_config"

    success "NixOS configuration.nix created and installed"
}

configure_nixos_instance() {
    local instance_name="$1"
    local username="$2"
    local password="$3"
    local ssh_key_path="$4"

    slog "Configuring NixOS instance: $instance_name"

    # Check if instance is running
    if ! incus list "$instance_name" --format csv | grep -q "RUNNING"; then
        slog "Starting instance $instance_name..."
        incus start "$instance_name"
        sleep 5
    fi

    # Wait for instance to be ready
    slog "Waiting for instance to be ready..."
    local max_attempts=30
    local attempt=0
    while [[ $attempt -lt $max_attempts ]]; do
        if incus exec "$instance_name" -- test -f /etc/nixos/configuration.nix 2>/dev/null; then
            break
        fi
        sleep 2
        ((attempt++))
    done

    if [[ $attempt -eq $max_attempts ]]; then
        fail "Instance $instance_name did not become ready in time"
        exit 1
    fi

    # Read SSH key content if provided
    local ssh_key_content=""
    if [[ -n "$ssh_key_path" && -f "$ssh_key_path" ]]; then
        ssh_key_content=$(cat "$ssh_key_path")
        slog "Using SSH key: $ssh_key_path"
    fi

    # Create and apply NixOS configuration
    create_nixos_configuration "$instance_name" "$username" "$ssh_key_content"

    # Apply the configuration
    slog "Applying NixOS configuration (this may take a few minutes)..."
    if incus exec "$instance_name" -- nixos-rebuild switch; then
        success "NixOS configuration applied successfully"
    else
        warn "NixOS configuration application had issues, but continuing..."
    fi

    # Ensure user exists and has correct password
    slog "Setting up user: $username"
    incus exec "$instance_name" -- useradd -m -G wheel,sudo -s /bin/bash "$username" 2>/dev/null || true
    echo "$username:$password" | incus exec "$instance_name" -- chpasswd

    # Set up SSH directory and keys
    if [[ -n "$ssh_key_content" ]]; then
        slog "Setting up SSH access for $username..."
        incus exec "$instance_name" -- mkdir -p "/home/<USER>/.ssh"
        echo "$ssh_key_content" | incus exec "$instance_name" -- tee "/home/<USER>/.ssh/authorized_keys" >/dev/null
        incus exec "$instance_name" -- chown -R "$username:$username" "/home/<USER>/.ssh"
        incus exec "$instance_name" -- chmod 700 "/home/<USER>/.ssh"
        incus exec "$instance_name" -- chmod 600 "/home/<USER>/.ssh/authorized_keys"
    fi

    # Ensure SSH service is running
    slog "Starting SSH service..."
    incus exec "$instance_name" -- systemctl enable --now sshd 2>/dev/null || true
    incus exec "$instance_name" -- systemctl enable --now ssh 2>/dev/null || true

    success "NixOS instance $instance_name configured successfully!"
}

show_completion_info() {
    local instance_name="$1"
    local username="$2"

    success "NixOS $SETUP_TYPE '$instance_name' is ready!"
    echo
    slog "Instance Details:"
    incus list "$instance_name"
    echo

    local ip
    ip=$(incus list "$instance_name" --format csv --columns 4 | head -1 | cut -d' ' -f1)

    if [[ -n "$ip" && "$ip" != "-" ]]; then
        slog "Access Information:"
        slog "  IP Address: $ip"
        slog "  Username: $username"
        slog "  Password: $DEFAULT_PASSWORD"
        echo
        slog "SSH Access:"
        slog "  ssh $username@$ip"
        if [[ -n "$DEFAULT_SSH_KEY" ]]; then
            slog "  SSH key authentication: ENABLED"
        fi
        slog "  Password authentication: ENABLED"
        echo
    fi

    slog "Direct Access:"
    slog "  Shell: incus exec $instance_name -- /bin/bash"
    slog "  User shell: incus exec $instance_name -- su - $username"
    echo

    slog "Instance Management:"
    slog "  Status: incus list $instance_name"
    slog "  Stop: incus stop $instance_name"
    slog "  Start: incus start $instance_name"
    slog "  Delete: incus delete $instance_name --force"
}

parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        --username)
            DEFAULT_USERNAME="$2"
            shift 2
            ;;
        --password)
            DEFAULT_PASSWORD="$2"
            shift 2
            ;;
        --ssh-key)
            DEFAULT_SSH_KEY="$2"
            shift 2
            ;;
        --container)
            SETUP_TYPE="container"
            shift
            ;;
        --vm)
            SETUP_TYPE="vm"
            shift
            ;;
        --help | -h)
            usage
            exit 0
            ;;
        -*)
            fail "Unknown option: $1"
            usage
            exit 1
            ;;
        *)
            if [[ -z "$CONTAINER_NAME" && -z "$VM_NAME" ]]; then
                if [[ "$SETUP_TYPE" == "container" ]]; then
                    CONTAINER_NAME="$1"
                elif [[ "$SETUP_TYPE" == "vm" ]]; then
                    VM_NAME="$1"
                else
                    # Auto-detect
                    detect_setup_type "$1"
                fi
            else
                fail "Multiple instance names provided"
                usage
                exit 1
            fi
            shift
            ;;
        esac
    done

    # Validate required arguments
    if [[ -z "$CONTAINER_NAME" && -z "$VM_NAME" ]]; then
        fail "Instance name is required"
        usage
        exit 1
    fi
}

main() {
    slog "Starting NixOS instance configuration..."

    parse_args "$@"

    # Determine instance name
    local instance_name
    if [[ -n "$CONTAINER_NAME" ]]; then
        instance_name="$CONTAINER_NAME"
    else
        instance_name="$VM_NAME"
    fi

    # Configure the instance
    configure_nixos_instance "$instance_name" "$DEFAULT_USERNAME" "$DEFAULT_PASSWORD" "$(ssh_key_path)"

    # Show completion information
    show_completion_info "$instance_name" "$DEFAULT_USERNAME"

    success "All done! Your NixOS $SETUP_TYPE '$instance_name' is ready to use."
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
