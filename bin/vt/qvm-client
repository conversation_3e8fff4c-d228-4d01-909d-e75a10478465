#!/bin/bash

# Create Client VM Script
VM_NAME="${1:-client-vm1}"
VM_MEMORY="${2:-1024}"
DISK_SIZE="${3:-10G}"
TAP_INTERFACE="${4:-tap-vm1}"

IMAGE_DIR="/var/lib/qemu/images"
VM_DIR="$IMAGE_DIR/$VM_NAME"

sudo mkdir -p "$VM_DIR"

# Download Ubuntu cloud image
UBUNTU_IMG="$IMAGE_DIR/ubuntu-22.04-server-cloudimg-amd64.img"
if [ ! -f "$UBUNTU_IMG" ]; then
    wget -O "/tmp/ubuntu-22.04-server-cloudimg-amd64.img" \
        "https://cloud-images.ubuntu.com/jammy/current/jammy-server-cloudimg-amd64.img"
    sudo mv "/tmp/ubuntu-22.04-server-cloudimg-amd64.img" "$UBUNTU_IMG"
fi

# Create VM disk from cloud image
sudo qemu-img create -f qcow2 -F qcow2 -b "$UBUNTU_IMG" "$VM_DIR/root-disk.qcow2" "$DISK_SIZE"

# Create cloud-init configuration
cat > "/tmp/user-data" << EOF
#cloud-config
hostname: $VM_NAME
users:
  - name: ubuntu
    sudo: ALL=(ALL) NOPASSWD:ALL
    ssh_authorized_keys:
      - $(cat ~/.ssh/id_rsa.pub)
package_update: true
packages:
  - curl
  - wget
  - vim
EOF

cat > "/tmp/meta-data" << EOF
instance-id: $VM_NAME
local-hostname: $VM_NAME
EOF

# Create cloud-init ISO
sudo cloud-localds "$VM_DIR/cloud-init.iso" "/tmp/user-data" "/tmp/meta-data"

# Generate MAC address
VM_MAC=$(printf '52:54:00:%02x:%02x:%02x' $((RANDOM%256)) $((RANDOM%256)) $((RANDOM%256)))

# Create startup script
cat > "$VM_DIR/start-vm.sh" << EOF
#!/bin/bash

sudo qemu-system-x86_64 \\
    -enable-kvm \\
    -m $VM_MEMORY \\
    -smp 1 \\
    -drive file=$VM_DIR/root-disk.qcow2,format=qcow2,if=virtio \\
    -drive file=$VM_DIR/cloud-init.iso,media=cdrom \\
    -netdev tap,id=net0,ifname=$TAP_INTERFACE,script=no,downscript=no \\
    -device virtio-net-pci,netdev=net0,mac=$VM_MAC \\
    -vnc :2 \\
    -serial stdio \\
    -name "$VM_NAME" \\
    -daemonize \\
    -pidfile $VM_DIR/vm.pid

echo "$VM_NAME started with PID \$(cat $VM_DIR/vm.pid)"
echo "Connect via VNC on localhost:5902"
EOF

chmod +x "$VM_DIR/start-vm.sh"

echo "Client VM $VM_NAME created!"
echo "MAC Address: $VM_MAC"
echo "Start with: $VM_DIR/start-vm.sh"

# Clean up
rm -f "/tmp/user-data" "/tmp/meta-data"
