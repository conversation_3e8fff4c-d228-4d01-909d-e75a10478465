#!/usr/bin/env bash

set -euo pipefail

trap 'echo "❌  Error on line $LINENO"; exit 1' ERR

cmd_vmu() {
  virsh --connect qemu:///session "$@"
}

check_prerequisites() {
  for cmd in virsh vmu-create trash; do
    if ! command -v "$cmd" >/dev/null 2>&1; then
      echo "Error: Required command '$cmd' is not available."
      exit 1
    fi
  done
}

usage() {
  cat <<EOF
Usage: $0 <command> [vm-name]

Manage VMs created with vmu-create script. These are user-mode VMs, not full libvirt VMs.

COMMANDS:
    list                List all VMs
    create  ARGS        Create a new VM, with same ARGS as vmu-create
    autostart <vm-name> Set VM to start on boot
    start <vm-name>     Start a VM
    shutdown <vm-name>  Gracefully stop a VM
    restart <vm-name>   Restart a VM
    kill <vm-name>      Force stop a VM
    delete <vm-name>    Delete a VM completely
    console <vm-name>   Connect to VM console
    ssh <vm-name>       Connect to VM via SSH
    info <vm-name>      Show VM status and info
    logs <vm-name>      Show cloud-init logs
    disk <vm-name>      Show VM disk usage
    cmd <virsh-args>    Run virsh command with qemu:///session connection

EXAMPLES:
    $0 list                    # List all VMs
    $0 show-ip debian          # Show IP address of 'debian' VM
    $0 status debian           # Show status of 'debian' VM
    $0 create debian           # Create debian VM
    $0 start debian            # Start 'debian' VM
    $0 delete old-vm           # Delete 'old-vm' completely
EOF
}

disk_vmu() {
  virsh --connect qemu:///session domblklist "$1"
}

status_vmu() {
  virsh --connect qemu:///session dominfo "$1"
}

autostart_vmu() {
  virsh --connect qemu:///session autostart "$1"
}

start_vmu() {
  virsh --connect qemu:///session start "$1"
}

shutdown_vmu() {
  virsh --connect qemu:///session shutdown "$1"
}

reboot_vmu() {
  virsh --connect qemu:///session reboot "$1"
}

force_stop_vmu() {
  virsh --connect qemu:///session destroy "$1"
}

delete_vmu() {
  local vda
  vda=$(virsh --connect qemu:///session domblklist "$1" | grep vda | awk '{print $2}')

  virsh --connect qemu:///session destroy "$1" >/dev/null 2>&1 || true
  virsh --connect qemu:///session undefine "$1" >/dev/null 2>&1 || true

  if [[ -n "$vda" ]]; then
    dir=$(dirname "$vda")

    echo "Found vm directory: $dir"
    read -p "Delete dir? (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
      echo "Deleting $dir"
      trash "$dir"
    fi
  fi
}

logs_vmu() {
  sudo virt-cat -d "$1" /var/log/cloud-init.log
}

console_vmu() {
  virsh --connect qemu:///session console "$1"
}

ssh_vmu() {
  local VM="$1"
  if [ -z "$VM" ]; then
    echo "Usage: vmu ssh <vm-name>"
    exit 1
  fi

  if [[ ! -f ~/.vm-ports ]]; then
    echo "No VMs found"
    exit 1
  fi

  local entry
  entry=$(grep -m1 "^$VM " ~/.vm-ports) || {
    echo "VM $VM not found"
    exit 1
  }

  local port user
  port=$(echo "$entry" | awk '{print $2}')
  user=$(echo "$entry" | awk '{print $3}')

  ssh -p "$port" "$user"@127.0.0.1
}

list_vmu() {
  virsh --connect qemu:///session list --all
}

main() {
  if [[ $# -eq 0 ]]; then
    usage
    exit 1
  fi

  check_prerequisites

  local command vm_name
  command="$1"
  vm_name="${2:-}"

  case "$command" in
  list | ls)
    list_vmu
    ;;
  cmd)
    cmd_vmu "${@:2}"
    ;;
  disk)
    disk_vmu "$vm_name"
    ;;
  info | status)
    status_vmu "$vm_name"
    ;;
  create | new)
    vmu-create "${@:2}"
    ;;
  autostart)
    autostart_vmu "$vm_name"
    ;;
  start | boot)
    start_vmu "$vm_name"
    ;;
  stop | shutdown)
    shutdown_vmu "$vm_name"
    ;;
  restart | reboot)
    reboot_vmu "$vm_name"
    ;;
  destroy | kill | force-stop)
    force_stop_vmu "$vm_name"
    ;;
  delete | rm)
    delete_vmu "$vm_name"
    ;;
  console)
    console_vmu "$vm_name"
    ;;
  ssh)
    ssh_vmu "$vm_name"
    ;;
  logs)
    logs_vmu "$vm_name"
    ;;
  --help | -h)
    usage
    ;;
  *)
    echo "Error: Unknown command: $command"
    usage
    ;;
  esac
}

main "$@"
