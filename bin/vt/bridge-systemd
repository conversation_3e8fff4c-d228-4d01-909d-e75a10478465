#! /usr/bin/env bash

# Create bridge setup service
sudo tee /etc/systemd/system/setup-bridges.service <<EOF
[Unit]
Description=Setup VM Bridges
After=network.target
Before=multi-user.target

[Service]
Type=oneshot
ExecStart=/usr/local/bin/setup-bridges.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# Create the setup script
sudo tee /usr/local/bin/setup-bridges.sh <<EOF
#!/bin/bash

# Setup WAN bridge
ip link add name br-wan type bridge 2>/dev/null || true
ip link set dev br-wan up
ip link set dev eth0 master br-wan 2>/dev/null || true
ip link set dev eth0 up

# Setup LAN bridge
ip link add name br-lan type bridge 2>/dev/null || true
ip link set dev br-lan up

# Create TAP interfaces
ip tuntap add dev tap-opn-wan mode tap user qemu 2>/dev/null || true
ip link set dev tap-opn-wan up
ip link set dev tap-opn-wan master br-wan

ip tuntap add dev tap-opn-lan mode tap user qemu 2>/dev/null || true
ip link set dev tap-opn-lan up
ip link set dev tap-opn-lan master br-lan

ip tuntap add dev tap-vm1 mode tap user qemu 2>/dev/null || true
ip link set dev tap-vm1 up
ip link set dev tap-vm1 master br-lan

ip tuntap add dev tap-vm2 mode tap user qemu 2>/dev/null || true
ip link set dev tap-vm2 up
ip link set dev tap-vm2 master br-lan

# Get DHCP for host connectivity
dhclient br-wan 2>/dev/null || true
EOF

sudo chmod +x /usr/local/bin/setup-bridges.sh
sudo systemctl enable setup-bridges.service
