#!/usr/bin/env bash

# shellcheck disable=SC1091

source "$DOT_DIR/share/utils"
source "$(dirname "$0")/vt-utils"

dt_exists() {
  local container_name="$1"
  distrobox list | grep -q "\b${container_name}\b" || return 1
}

dt_check_exists() {
  local container_name="$1"
  dt_exists "$container_name" || err_exit "Container '$container_name' not found"
}

dt_list() {
  slog "Listing all distrobox containers..."

  echo
  distrobox list

  if has_cmd podman; then
    echo
    echo
    echo
    slog "Podman container details:"
    podman ps -a --filter "label=manager=distrobox" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedHuman}}" 2>/dev/null || true
  elif has_cmd docker; then
    echo
    echo
    echo
    slog "Docker container details:"
    docker ps -a --filter "label=manager=distrobox" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" 2>/dev/null || true
  fi
}

dt_state() {
  local container_name="$1"

  dt_check_exists "$container_name"

  slog "Status for container '$container_name':"

  local engine
  engine=$(distrobox list | grep "\b${container_name}\b" | awk '{print $NF}' | head -1)
  if [[ -n "$engine" ]]; then
    slog "Container engine details:"
    if has_cmd podman; then
      podman ps -a --filter "name=${container_name}" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedHuman}}" 2>/dev/null || true
    elif has_cmd docker; then
      docker ps -a --filter "name=${container_name}" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" 2>/dev/null || true
    fi
  fi
}
