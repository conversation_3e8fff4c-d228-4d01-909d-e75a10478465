#!/usr/bin/env bash

set -euo pipefail

# shellcheck disable=SC1091
source "$(dirname "$0")/dt-utils"

DT_DISTRO_LIST=("ubuntu" "fedora" "arch" "debian" "alpine" "nix")

SESSION_NAME="DT_TMUX"

usage() {
    cat <<EOF
Usage: $(basename "$0") [OPTION]

Manage a tmux session with connections to distrobox containers.

Options:
  create    Create a new tmux session with connections to containers (default if no option)
  attach    Attach to an existing session
  detach    Detach from the current session
  destroy   Kill the tmux session
  help      Display this help message

Examples:
  $(basename "$0")           # Create session or attach if exists
  $(basename "$0") create    # Force create a new session
  $(basename "$0") attach    # Attach to existing session
  $(basename "$0") detach    # Detach from current session
  $(basename "$0") destroy   # Kill the session
EOF
}

check_distrobox() {
    has_cmd distrobox || err_exit "distrobox is not installed. Please install it first."
}

dt_exists() {
    local container_name="$1"
    distrobox list | grep -q "^$container_name "
    return $?
}

dt_check_exists() {
    local container_name="$1"
    if ! dt_exists "$container_name"; then
        fail "Container '$container_name' does not exist"
        return 1
    fi
    return 0
}

create_session() {
    tmux_session "$SESSION_NAME" "$1"

    slog "Creating tmux session '$SESSION_NAME' with SSH connections to Distrobox containers..."

    local ssh_cmds=()
    create_commands ssh_cmds "dt" "${DT_DISTRO_LIST[@]}"

    start_sessions "dt" "${DT_DISTRO_LIST[@]}"

    if ! tmux_grid "$SESSION_NAME" "${ssh_cmds[@]}"; then
        fail "Failed to create tmux session '$SESSION_NAME' with SSH connections to containers."
        return 1
    fi

    if ! tmux attach-session -t "$SESSION_NAME"; then
        fail "Failed to attach to tmux session '$SESSION_NAME'."
        return 1
    fi
}

main() {
    check_distrobox
    check_tmux

    local command="${1:-}"

    case "$command" in
    create | -c | --create | "")
        create_session "true"
        ;;
    attach)
        attach_session "$SESSION_NAME"
        ;;
    detach)
        detach_session "$SESSION_NAME"
        ;;
    destroy)
        destroy_session "$SESSION_NAME"
        ;;
    help | --help | -h)
        usage
        ;;
    *)
        fail "Unknown option: $command"
        usage
        exit 1
        ;;
    esac
}

main "$@"
