#!/usr/bin/env bash

# shellcheck disable=SC1091
source "$(dirname "$0")/vt-utils"

virt_check_prerequisites() {
  local missing_tools=()
  for tool in virsh virt-install qemu-img wget xorriso openssl; do
    if ! has_cmd "$tool"; then
      missing_tools+=("$tool")
    fi
  done

  if [[ ${#missing_tools[@]} -gt 0 ]]; then
    fail "Missing required tools: ${missing_tools[*]}"
    slog "Install with: vm install"
    exit 1
  fi

  if ! systemctl is-active --quiet libvirtd; then
    warn "libvirtd service is not running, Starting..."
    sudo systemctl start libvirtd
    sleep 1

    if ! systemctl is-active --quiet libvirtd; then
      fail "Failed to start libvirtd service"
      exit 1
    else
      slog "libvirtd service started"
    fi
  fi

  if ! groups | grep -q libvirt; then
    fail "User not in libvirt group. You may need sudo for virsh commands"
    fail "Add user to group: sudo usermod -a -G libvirt \$USER"
    exit 1
  fi
}

vm_check_prerequisites() {
  virt_check_prerequisites

  if ! has_cmd vm-create; then
    fail "vm-create is not in PATH."
    exit 1
  fi

  if ! has_cmd vm; then
    fail "vm is not in PATH."
    exit 1
  fi
}

vm_list() {
  slog "Listing all VMs..."
  echo
  if has_cmd virsh; then
    virsh list --all
  else
    fail "virsh command not found. Please install virtualization tools first."
    slog "You can install them with: vm install"
    return 1
  fi
}

vm_exists() {
  virsh dominfo "$1" &>/dev/null
}

vm_check_exists() {
  local vm_name="$1"

  vm_exists "$vm_name" && return 0

  fail "VM '$vm_name' not found"
  return 1
}

vm_state() {
  virsh domstate "$vm_name"
}

vm_ip() {
  local vm_name="$1"
  local ip
  local mac
  local network
  local mac_addrs
  local state

  vm_exists "$vm_name" || return 1

  state=$(virsh domstate "$vm_name" 2>/dev/null)
  if [[ "$state" != "running" ]]; then
    return 2
  fi

  # Method 1: QEMU Guest Agent
  ip=$(virsh domifaddr "$vm_name" --source agent 2>/dev/null | awk '/ipv4/ {print $4}' | cut -d'/' -f1 | grep -v '^127\.0\.0\.1$' | head -n 1)
  if [[ -n "$ip" ]]; then
    echo "$ip"
    return 0
  fi

  # Method 2: DHCP Leases based on MAC Address
  mac_addrs=$(virsh domiflist "$vm_name" | awk 'NR>2 {print $5}')

  if [[ -n "$mac_addrs" ]]; then
    for mac in $mac_addrs; do
      # Find the network associated with the MAC address.
      network=$(virsh domiflist "$vm_name" | grep -i "$mac" | awk '{print $3}')

      if [[ -n "$network" && "$network" != "-" ]]; then
        # Check for IP using the specific network and MAC.
        if virsh net-dhcp-leases --help | grep -q -- '--mac'; then
          ip=$(virsh net-dhcp-leases "$network" --mac "$mac" 2>/dev/null | awk 'NR>2 {print $5}' | cut -d'/' -f1 | head -n 1)
        else
          # Fallback for older libvirt versions that don't support --mac.
          ip=$(virsh net-dhcp-leases "$network" 2>/dev/null | grep -i "$mac" | awk '{print $5}' | cut -d'/' -f1 | head -n 1)
        fi

        if [[ -n "$ip" ]]; then
          echo "$ip"
          return 0
        fi
      fi
    done

    # If IP not found on the primary network, check all active networks.
    for mac in $mac_addrs; do
      for network in $(virsh net-list --name); do
        if virsh net-dhcp-leases --help | grep -q -- '--mac'; then
          ip=$(virsh net-dhcp-leases "$network" --mac "$mac" 2>/dev/null | awk 'NR>2 {print $5}' | cut -d'/' -f1 | head -n 1)
        else
          ip=$(virsh net-dhcp-leases "$network" 2>/dev/null | grep -i "$mac" | awk '{print $5}' | cut -d'/' -f1 | head -n 1)
        fi

        if [[ -n "$ip" ]]; then
          echo "$ip"
          return 0
        fi
      done
    done
  fi

  # Method 3: Fallback to original script's less reliable grep method
  # This is a last resort and might be inaccurate.
  ip=$(virsh net-dhcp-leases default 2>/dev/null | grep "$vm_name" | awk '{print $5}' | cut -d'/' -f1 | head -1)
  if [[ -n "$ip" ]]; then
    echo "$ip"
    return 0
  fi

  return 3
}

ip_errors() {
  local ret=$1
  local vm_name=$2

  if [[ $ret -eq 1 ]]; then
    fail "VM '$vm_name' not found"
    return 1
  elif [[ $ret -eq 2 ]]; then
    fail "VM '$vm_name' is not running"
    return 1
  elif [[ $ret -eq 3 ]]; then
    fail "Could not determine IP address for VM '$vm_name'"
    return 1
  fi

}

vm_ssh() {
  local vm_name="$1"
  local username="${2:-}"
  vm_check_exists "$vm_name" || return 1

  if [[ -z "$vm_name" ]]; then
    fail "VM name not provided. Usage: vm ssh <vm_name> [username]"
    return 1
  fi

  if [[ -z "$username" ]]; then
    fail "Username not provided. Usage: vm ssh <vm_name> [username]"
    return 1
  fi

  local state
  state=$(virsh domstate "$vm_name")

  if [[ "$state" != "running" ]]; then
    fail "VM '$vm_name' is not running"
    slog "Start it with: $0 start $vm_name"
    return 1
  fi

  local ip
  ip=$(vm_ip "$vm_name")
  local ret=$?
  ip_errors "$ret" "$vm_name"

  slog "Connecting to $vm_name ($ip) as $username..."
  ssh "$username@$ip"
}
