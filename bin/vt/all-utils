#!/usr/bin/env bash

all_usage() {
  local vt="$1"
  local type="$2"

  cat <<EOF
Usage: $(basename "$0") [OPTION]

"${vt}" {$type} for various Linux distributions.

Options:
  create    # Create all "${vt}" {$type} for various Linux distributions.(default if no option)
  delete    # Delete all "${vt}" {$type} for various Linux distributions.
  start     # Start all "${vt}" {$type} for various Linux distributions.
  stop      # Stop all "${vt}" {$type} for various Linux distributions.
  restart   # Restart all "${vt}" {$type} for various Linux distributions.
  --help    # Display this help message

Examples:
  $(basename "$0")           # Create {$type} for various Linux distributions.or attach if exists
  $(basename "$0") create    # Force create a new set of {$type} for various Linux distributions.}
  $(basename "$0") delete    # Delete all {$type} for various Linux distributions.}
  $(basename "$0") start     # Start all {$type} for various Linux distributions.
  $(basename "$0") stop      # Stop all {$type} for various Linux distributions.
  $(basename "$0") restart   # Restart all {$type} for various Linux distributions.}
EOF
}

all_parse_args() {
  if [[ $# -eq 0 ]]; then
    all_create "$@"
    exit 0
  fi

  if [[ "$1" == "delete" ]]; then
    all_delete
  elif [[ "$1" == "start" ]]; then
    all_start
  elif [[ "$1" == "stop" ]]; then
    all_stop
  elif [[ "$1" == "restart" ]]; then
    all_restart
  elif [[ "$1" == "--help" || "$1" == "help" || "$1" == "help" ]]; then
    usage
    exit 0
  elif [[ $# -eq 0 ]] || [[ "$1" == "create" ]]; then
    all_create
  else
    fail "Unknown option: $1"
    usage
    exit 1
  fi
}
