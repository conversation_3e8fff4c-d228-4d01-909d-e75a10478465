#!/usr/bin/env bash

# shellcheck disable=SC1091
source "$(dirname "$0")/vt-utils"

ivm_check_exists_prerequisites() {
  local missing=()
  for cmd in incus openssl curl wget tee mktemp; do
    if ! has_cmd "$cmd"; then
      missing+=("$cmd")
    fi
  done

  if ((${#missing[@]})); then
    fail "Missing required commands: ${missing[*]}"
    exit 1
  fi

  if ! incus info >/dev/null 2>&1; then
    fail "Cannot connect to Incus daemon. Please ensure Incus is running and you have proper permissions."
    exit 1
  fi

  has_cmd ivm || {
    fail "ivm is not in PATH."
    exit 1
  }

  has_cmd ivm-create || {
    fail "ivm-create is not in PATH."
    exit 1
  }
}

ivm_state() {
  incus info "$1" | grep "Status:" | awk '{print $2}'
}

ivm_exists() {
  incus info "$1" >/dev/null 2>&1
}

ivm_check_exists() {
  if ! ivm_exists "$1"; then
    fail "VM '$1' not found"
    return 1
  fi
}

ivm_running() {
  local vm_name="$1"
  local state
  state=$(ivm_state "$vm_name")
  [[ "$state" == "RUNNING" ]]
}

ivm_check_exists_running() {
  local vm_name="$1"
  if ! ivm_running "$vm_name"; then
    fail "VM '$vm_name' is not running (state: $state)"
    return 1
  fi
}

ivm_list() {
  local vm_name="${1:-}"
  local extra_args="${2:---columns=ns4}"

  if [[ -n "$vm_name" ]]; then
    incus list type=virtual-machine "$vm_name" "${extra_args}"
  else
    incus list type=virtual-machine "${extra_args}"
  fi
}

# wait_for_vm_state() {
#   local vm_name="$1"
#   local expected_state="$2"
#   local timeout="${3:-60}"
#   local interval="${4:-3}"

#   local count=0
#   local max_attempts=$((timeout / interval))

#   while ((count < max_attempts)); do
#     local state
#     state=$(ivm_state "$vm_name")
#     if [[ "$state" == "$expected_state" ]]; then
#       return 0
#     fi
#     sleep "$interval"
#     ((count++))
#   done
#   return 1
# }

# ivm_ip() {
#     local vm_name="$1"

#     if ! incus info "$vm_name" &>/dev/null; then
#         return 1
#     fi

#     local state
#     state=$(incus info "$vm_name" | grep "Status:" | awk '{print $2}')

#     if [[ "$state" != "Running" ]]; then
#         return 2
#     fi

#     local ip
#     ip=$(incus list "$vm_name" --format csv -c 4 | cut -d' ' -f1)

#     if [[ -z "$ip" || "$ip" == "-" ]]; then
#         return 3
#     fi

#     echo "$ip"
#     return 0
# }

# ivm_ip() {
#     slog "Getting VM IP address..."

#     local ip
#     local max_attempts=30
#     local attempt=0

#     while [[ $attempt -lt $max_attempts ]]; do
#         ip=$(incus list "$VM_NAME" --format csv --columns 4 | head -1 | cut -d' ' -f1)

#         if [[ -n "$ip" && "$ip" != "-" ]]; then
#             echo "$ip"
#             return 0
#         fi

#         sleep 2
#         ((attempt++))
#     done

#     warn "Could not determine VM IP address"
#     return 1
# }

ivm_ip() {
  local vm_name="$1"

  if ! incus info "$vm_name" >/dev/null 2>&1; then
    return 1
  fi

  local state
  state=$(ivm_state "$vm_name")

  if [[ "$state" != "RUNNING" ]]; then
    return 2
  fi

  # Try to get IP from incus list with network info
  local ip
  ip=$(incus list "^${vm_name}$" --format csv --columns 4 | head -1 | cut -d',' -f1 | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)

  # If that fails, try getting it from incus info
  if [[ -z "$ip" ]]; then
    ip=$(incus info "$vm_name" | grep -A 20 "Network usage:" | grep -oE 'inet ([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1 | cut -d' ' -f2)
  fi

  # If still no IP, try alternative method
  if [[ -z "$ip" ]]; then
    ip=$(incus exec "$vm_name" -- ip -4 addr show | grep -oE 'inet ([0-9]{1,3}\.){3}[0-9]{1,3}' | grep -v '127.0.0.1' | head -1 | cut -d' ' -f2 2>/dev/null)
  fi

  if [[ -z "$ip" ]]; then
    return 3
  fi

  echo "$ip"
  return 0
}

ivm_show_ip() {
  local vm_name="$1"
  ivm_check_exists "$vm_name" || return 1

  local ip
  ip=$(ivm_ip "$vm_name")
  local ret=$?

  case $ret in
  1)
    fail "VM '$vm_name' not found"
    return 1
    ;;
  2)
    fail "VM '$vm_name' is not running"
    slog "Start it with: $0 start $vm_name"
    return 1
    ;;
  3)
    fail "Could not determine IP address for VM '$vm_name'"
    slog "VM may still be starting up. Try again in a few moments."
    return 1
    ;;
  0)
    echo "$ip"
    return 0
    ;;
  esac
}

ivm_ssh() {
  local vm_name="$1"
  local username="${2:-}"
  ivm_check_exists "$vm_name" || return 1

  ivm_check_exists_running "$vm_name" || return 1

  if [[ -z "$username" ]]; then
    fail "Username required. Usage: $0 ssh <vm_name> <username>"
    return 1
  fi

  local ip
  ip=$(ivm_ip "$vm_name")
  local ret=$?

  case $ret in
  1)
    fail "VM '$vm_name' not found"
    return 1
    ;;
  2)
    fail "VM '$vm_name' is not running"
    slog "Start it with: $0 start $vm_name"
    return 1
    ;;
  3)
    fail "Could not determine IP address for VM '$vm_name'"
    slog "VM may still be starting up. Try again in a few moments."
    return 1
    ;;
  0)
    slog "Connecting to $vm_name ($ip) as $username..."
    ssh "$username@$ip"
    ;;
  esac
}
