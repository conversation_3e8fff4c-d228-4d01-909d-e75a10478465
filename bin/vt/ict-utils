#!/usr/bin/env bash

# shellcheck disable=SC1091
source "$(dirname "$0")/vt-utils"

ict_check_prerequisites() {
  local missing=()
  for cmd in incus openssl curl wget tee mktemp; do
    if ! has_cmd "$cmd"; then
      missing+=("$cmd")
    fi
  done

  if ((${#missing[@]})); then
    fail "Missing required commands: ${missing[*]}"
    exit 1
  fi

  if ! incus info >/dev/null 2>&1; then
    fail "Cannot connect to Incus daemon. Please ensure Incus is running and you have proper permissions."
    exit 1
  fi

  has_cmd ict-create || {
    fail "ict-create is not in PATH."
    exit 1
  }

  has_cmd ict || {
    fail "ict is not in PATH."
    exit 1
  }
}

ict_list() {
  incus_check || return 1

  local extra_args="${2:---columns=ns4}"

  slog "Listing all Incus containers..."
  echo

  incus list type=container "${extra_args}"
}

ict_exists() {
  incus list type=container --format csv --columns n | grep -q "^${1}$"
}

ict_check_exists() {
  local container_name="$1"
  if ! ict_exists "$container_name"; then
    fail "Container '$container_name' not found"
    return 1
  fi
}
ict_state() {
  local container_name="$1"
  incus list type=container "$container_name" --format csv --columns s | head -1
}

ict_running() {
  local container_name="$1"
  local state
  state=$(ict_state "$container_name")
  [[ "$state" == "RUNNING" ]]
}

ict_check_running() {
  local container_name="$1"
  if ! ict_running "$container_name"; then
    fail "Container '$container_name' is not running (state: $(ict_state "$container_name"))"
    return 1
  fi
}

ict_ip() {
  local container_name="$1"

  if ! ict_exists "$container_name"; then
    return 1
  fi

  if ! ict_running "$container_name"; then
    return 2
  fi

  local ip
  ip=$(incus list "^${container_name}$" --format csv --columns 4 | head -1 | cut -d',' -f1 | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)

  if [[ -z "$ip" ]]; then
    return 3
  fi

  echo "$ip"
  return 0
}

ict_ssh() {
  local container_name="$1"
  local username="${2:-}"
  ict_check_exists "$container_name" || return 1

  if ! ict_running "$container_name"; then
    fail "Container '$container_name' is not running"
    slog "Start it with: $0 start $container_name"
    return 1
  fi

  # Auto-detect username if not provided
  if [[ -z "$username" ]]; then
    username=$(detect_container_user "$container_name")
    slog "Auto-detected username: $username (override with: $0 ssh $container_name <username>)"
  fi

  local ip
  if ip=$(ict_ip "$container_name"); then
    slog "Connecting to $container_name ($ip) as $username..."
    ssh "$username@$ip"
    return 0
  else
    local ret=$?

    case $ret in
    1)
      fail "Container '$container_name' not found"
      ;;
    2)
      fail "Container '$container_name' is not running"
      slog "Start it with: $0 start $container_name"
      ;;
    3)
      fail "Could not determine IP address for container '$container_name'"
      slog "Container may still be starting up. Try again in a few moments."
      ;;
    *)
      fail "Unexpected error retrieving IP for container '$container_name'"
      return 1
      ;;
    esac

    return 1
  fi

}
