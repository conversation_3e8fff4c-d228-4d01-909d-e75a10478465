#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/dt-utils"
source "$(dirname "$0")/all-utils"

DT_DISTRO_LIST=("ubuntu" "debian" "arch" "fedora" "alpine" "tw" "nix" "centos" "rocky" "alpine")

dt_exists() {
  distrobox list | grep -q "$1" >/dev/null
}

check_prerequisites() {
  has_cmd distrobox || err_exit "distrobox is not installed. Please install it first."
  has_cmd dt || err_exit "dt is not in PATH."
}

create_all() {
  slog "Creating Distrobox containers (Ubuntu, Fedora, Arch, Debian, Alpine, Tumbleweed, NixOS)..."

  for d in "${DT_DISTRO_LIST[@]}"; do
    if dt_exists "${d}-dt"; then
      slog "Container for ${d}-dt already exists, skipping..."
    else
      slog "Creating container for ${d}-dt..."
      dt create "$d" "${d}-dt"
    fi
  done

  slog "Listing created containers:"
  dt list

  success "All containers created successfully!"
  slog "You can access them using: dt enter <container-name>"
}

delete_all() {
  slog "Deleting all containers..."
  for ct in "${DT_DISTRO_LIST[@]}"; do
    if dt_exists "$ct-dt"; then
      slog "Deleting container: $ct-dt"
      dt delete "$ct-dt"
    fi
  done
  success "All containers deleted successfully!"
}

start_all() {
  slog "Starting all containers..."
  for ct in "${DT_DISTRO_LIST[@]}"; do
    if dt_exists "$ct-dt"; then
      slog "Starting container: $ct-dt"
      dt start "$ct-dt"
    fi
  done
  success "All containers started successfully!"
}

stop_all() {
  slog "Stopping all containers..."
  for ct in "${DT_DISTRO_LIST[@]}"; do
    if dt_exists "$ct-dt"; then
      slog "Stopping container: $ct-dt"
      dt stop "$ct-dt"
    fi
  done
  success "All containers stopped successfully!"
}

restart_all() {
  slog "Restarting all containers..."
  for ct in "${DT_DISTRO_LIST[@]}"; do
    if dt_exists "$ct-dt"; then
      slog "Restarting container: $ct-dt"
      dt restart "$ct-dt"
      sleep 1
    fi
  done
  success "All containers restarted successfully!"
}

usage() {
  all_usage "Distrobox" "containers"
}

main() {
  check_prerequisites
  all_parse_args "$@"
}

main "$@"
