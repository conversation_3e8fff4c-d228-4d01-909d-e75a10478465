#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/dck-utils"
source "$(dirname "$0")/all-utils"

DCK_DISTRO_LIST=("ubuntu" "debian" "arch" "fedora" "rocky" "tw" "alpine" "centos" "nix")

check_dck() {
  has_cmd docker || err_exit "docker is not installed. Please install it first."
}

dck_exists() {
  local container_name="$1"
  docker ps -a --format '{{.Names}}' | grep -q "^$container_name$"
}

all_create() {
  check_dck

  slog "Creating Docker containers for various Linux distributions..."

  for d in "${DCK_DISTRO_LIST[@]}"; do
    if ! dck_exists "$d"; then
      slog "Creating container for $d..."
      dck create "$d" "$d"
    else
      slog "Container for $d already exists, skipping..."
    fi
  done

  slog "Listing created containers:"
  docker ps -a

  success "All containers created successfully!"
  slog "You can access them using: docker exec -it <container-name> /bin/bash"
}

all_delete() {
  check_dck

  slog "Deleting all containers..."
  for ct in "${DCK_DISTRO_LIST[@]}"; do
    if dck_exists "$ct"; then
      slog "Deleting container: $ct"
      dck delete "$ct"
    else
      slog "Container $ct does not exist, skipping..."
    fi
  done
  success "All containers deleted successfully!"
}

all_start() {
  check_dck

  slog "Starting all containers..."
  for ct in "${DCK_DISTRO_LIST[@]}"; do
    if dck_exists "$ct"; then
      slog "Starting container: $ct"
      dck start "$ct"
    else
      slog "Container $ct does not exist, skipping..."
    fi
  done
  success "All containers started successfully!"
}

all_stop() {
  check_dck

  slog "Stopping all containers..."
  for ct in "${DCK_DISTRO_LIST[@]}"; do
    if dck_exists "$ct"; then
      slog "Stopping container: $ct"
      dck stop "$ct"
    else
      slog "Container $ct does not exist, skipping..."
    fi
  done
  success "All containers stopped successfully!"
}

all_restart() {
  check_dck

  slog "Restarting all containers..."
  for ct in "${DCK_DISTRO_LIST[@]}"; do
    if dck_exists "$ct"; then
      slog "Restarting container: $ct"
      dck restart "$ct"
      sleep 1
    else
      slog "Container $ct does not exist, skipping..."
    fi
  done
  success "All containers restarted successfully!"
}

usage() {
  all_usage "docker" "containers"
}

main() {
  check_dck

  all_parse_args "$@"
}

main "$@"
