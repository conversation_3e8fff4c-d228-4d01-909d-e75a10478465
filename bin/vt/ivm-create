#!/usr/bin/env bash

# shellcheck disable=SC1091

set -euo pipefail

source "$(dirname "$0")/ivm-utils"

trap 'echo "❌  Error on line $LINENO"; exit 1' ERR

unset VM_NAME
unset <PERSON>IS<PERSON>O
unset RELEASE
unset USER_NAME
unset PA<PERSON>WORD
unset SSH_KEY
unset IMAGE

VCPUS="3"
MEMORY_MB="4096"
DISK_SIZE="20GB"
BRIDGE_IF="incusbr0"

usage() {
    cat <<EOF
Usage: $0 --distro DISTRO [OPTIONS]

Create Incus virtual machines with cloud-init and SSH access.

REQUIRED:
    --distro DISTRO         Distribution (ubuntu, fedora, arch, debian, centos, tumbleweed, alpine)

OPTIONS:
    --name NAME             VM name (default: distro name)
    --release RELEASE       Distribution release (default: latest)
    --username USER         Username for VM (default: distro default)
    --password PASS         User password (default: vm name)
    --vcpus NUM             Number of vCPUs (default: 4)
    --memory MB             RAM in MB (default: 4096)
    --disk-size SIZE        Disk size (default: 20GB)
    --ssh-key PATH          SSH public key path (default: auto-detect)
    --bridge BRIDGE         Network bridge (default: incusbr0)
    --help, -h              Show this help

EXAMPLES:
    $0 --distro ubuntu
    $0 --distro fedora --name my-fedora --vcpus 4 --memory 4096
    $0 --distro debian --username admin --password mypass
    $0 --distro arch --release current --disk-size 40GB
    $0 --distro tumbleweed --name opensuse-vm --vcpus 2 --memory 4096

SUPPORTED DISTRIBUTIONS:
    ubuntu      - Ubuntu (plucky/25.04)
    fedora      - Fedora (latest)
    arch        - Arch Linux (current)
    debian      - Debian (13/trixie)
    centos      - CentOS Stream (9)
    tumbleweed  - openSUSE Tumbleweed (rolling release)
    alpine      - Alpine Linux (latest)
EOF
}

configure_distribution() {
    case "$DISTRO" in
    ubuntu)
        RELEASE=${RELEASE:-"plucky"}
        IMAGE="images:ubuntu/${RELEASE}/cloud"
        ;;
    fedora)
        RELEASE=${RELEASE:-"42"}
        IMAGE="images:fedora/${RELEASE}/cloud"
        ;;
    arch)
        RELEASE=${RELEASE:-"current"}
        IMAGE="images:archlinux/${RELEASE}/cloud"
        ;;
    debian)
        RELEASE=${RELEASE:-"13"}
        IMAGE="images:debian/${RELEASE}/cloud"
        ;;
    centos)
        RELEASE=${RELEASE:-"9-Stream"}
        IMAGE="images:centos/${RELEASE}/cloud"
        ;;
    tumbleweed | tw)
        RELEASE=${RELEASE:-"current"}
        IMAGE="images:opensuse/tumbleweed/cloud"
        ;;
    nix)
        RELEASE=${RELEASE:-"unstable"}
        IMAGE="images:nixos/${RELEASE}/cloud"
        ;;
    rocky)
        RELEASE=${RELEASE:-"9"}
        IMAGE="images:rockylinux/${RELEASE}/cloud"
        ;;
    alpine)
        RELEASE=${RELEASE:-"3.22"}
        IMAGE="images:alpine/${RELEASE}/cloud"
        ;;
    *)
        fail "Unsupported distribution: $DISTRO"
        fail "Supported distributions: ubuntu, fedora, arch, debian, centos, tumbleweed, alpine"
        exit 1
        ;;
    esac

    VM_NAME=${VM_NAME:-"${DISTRO}-vm"}
    USER_NAME=${USER_NAME:=$(default_username "$DISTRO")}
    PASSWORD=${PASSWORD:-"$USER_NAME"}
    PASSWORD_HASH=$(openssl passwd -6 "$PASSWORD")

    slog "Configuration:"
    slog "  Distribution: $DISTRO $RELEASE"
    slog "  VM Name: $VM_NAME"
    slog "  Username: $USER_NAME"
    slog "  Image: $IMAGE"
    slog "  Resources: ${VCPUS} vCPUs, ${MEMORY_MB}MB RAM, ${DISK_SIZE} disk"
    slog "  Network Bridge: $BRIDGE_IF"
}

generate_cloud_init_config() {
    slog "Generating cloud-init configuration..."

    CLOUD_INIT_DIR=$(mktemp -d)
    trap '[[ -n "$CLOUD_INIT_DIR" && -d "$CLOUD_INIT_DIR" ]] && rm -rf "$CLOUD_INIT_DIR"' EXIT

    local pub_key
    pub_key=$(cat "$SSH_KEY")

    local packages_common="qemu-guest-agent bash curl"

    local openssh_pkg

    if [[ "$DISTRO" == "arch" ]]; then
        openssh_pkg="openssh"
    else
        openssh_pkg="openssh-server"
    fi

    local runcmd_lines

    if [[ "$DISTRO" == "alpine" ]]; then
        runcmd_lines=(
            "rc-update add qemug-guest-agent default"
            "rc-update add sshd default"
        )
    else
        runcmd_lines=(
            "systemctl enable --now ssh || systemctl enable --now sshd || true"
        )
    fi

    cat >"${CLOUD_INIT_DIR}/user-data" <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

users:
  - name: $USER_NAME
    groups:
      - sudo
      - wheel
      - adm
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - "$pub_key"

package_update: true
package_upgrade: true

packages:
  - $openssh_pkg
$(for pkg in $packages_common; do echo "  - $pkg"; done)

runcmd:
$(printf "  - %s\n" "${runcmd_lines[@]}")

ssh_pwauth: true
disable_root: false

final_message: "VM $VM_NAME setup complete! SSH access is ready."
EOF

    cat >"${CLOUD_INIT_DIR}/meta-data" <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

    success "Cloud-init configuration files created in: $CLOUD_INIT_DIR"
}

create_vm() {
    slog "Creating Incus VM '$VM_NAME'..."

    if incus_instance_exists "$VM_NAME"; then
        fail "instance '$VM_NAME' already exists"
        exit 1
    fi

    generate_cloud_init_config

    slog "Launching VM with image: $IMAGE"

    local launch_cmd=(
        incus launch "$IMAGE" "$VM_NAME" --vm
        --config "limits.cpu=$VCPUS"
        --config "limits.memory=${MEMORY_MB}MB"
        --config "user.user-data=$(cat "${CLOUD_INIT_DIR}/user-data")"
        --config "user.meta-data=$(cat "${CLOUD_INIT_DIR}/meta-data")"
        --device "root,size=$DISK_SIZE"
        --config "security.secureboot=false"
        --network "$BRIDGE_IF"
    )

    if ! "${launch_cmd[@]}"; then
        fail "Failed to create VM '$VM_NAME'"
        exit 1
    fi

    success "VM '$VM_NAME' created successfully"

    if [[ -n "$CLOUD_INIT_DIR" && -d "$CLOUD_INIT_DIR" ]]; then
        rm -rf "$CLOUD_INIT_DIR"
        slog "Cleaned up temporary cloud-init files"
    fi
}

show_completion_info() {
    success "VM '$VM_NAME' will be ready in a few minutes!"

    echo
    slog "VM Management:"
    slog "  Status: ivm status $VM_NAME"
    slog "  Stop: ivm stop $VM_NAME"
    slog "  Start: ivm start $VM_NAME"
    slog "  Delete: ivm delete $VM_NAME"

    slog "Use username: $USER_NAME and password: $PASSWORD to login if needed."
}

parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
        --distro)
            DISTRO="$2"
            shift 2
            ;;
        --name)
            VM_NAME="$2"
            shift 2
            ;;
        --release)
            RELEASE="$2"
            shift 2
            ;;
        --username)
            USER_NAME="$2"
            shift 2
            ;;
        --password)
            PASSWORD="$2"
            shift 2
            ;;
        --vcpus)
            VCPUS="$2"
            shift 2
            ;;
        --memory)
            MEMORY_MB="$2"
            shift 2
            ;;
        --disk-size)
            DISK_SIZE="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --bridge)
            BRIDGE_IF="$2"
            shift 2
            ;;
        --help | -h)
            usage
            exit 0
            ;;
        *)
            fail "Unknown option: $1"
            usage
            exit 1
            ;;
        esac
    done

    if [[ -z "${DISTRO:-}" ]]; then
        fail "Distribution is required. Use --distro option."
        usage
        exit 1
    fi
}

main() {
    slog "Starting Incus VM creation..."
    ivm_check_exists_prerequisites
    parse_args "$@"

    if [[ -z "${SSH_KEY:-}" ]]; then
        SSH_KEY=$(ssh_key_path)
    fi

    if [[ ! -f "$SSH_KEY" ]]; then
        fail "SSH public key not found at: $SSH_KEY"
        exit 1
    fi

    configure_distribution
    create_vm
    show_completion_info
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
