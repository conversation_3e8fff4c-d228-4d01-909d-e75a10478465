# Create LAN bridge for internal VMs
sudo ip link add name br-lan type bridge
sudo ip link set dev br-lan up

# Configure bridge netfilter settings
sudo modprobe br_netfilter
echo "br_netfilter" | sudo tee /etc/modules-load.d/br_netfilter.conf

# Disable netfilter on bridges to avoid iptables interference
sudo sysctl -w net.bridge.bridge-nf-call-iptables=0
sudo sysctl -w net.bridge.bridge-nf-call-ip6tables=0
sudo sysctl -w net.bridge.bridge-nf-call-arptables=0

# Make settings persistent
cat << EOF | sudo tee /etc/sysctl.d/10-bridge.conf
net.bridge.bridge-nf-call-iptables=0
net.bridge.bridge-nf-call-ip6tables=0
net.bridge.bridge-nf-call-arptables=0
net.ipv4.ip_forward=1
EOF
