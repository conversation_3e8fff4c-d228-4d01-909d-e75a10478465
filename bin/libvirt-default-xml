#! /usr/bin/env bash

set -euo pipefail

UUID=$(uuidgen)
MAC=$(printf '52:54:00:%02x:%02x:%02x' $((RANDOM % 256)) $((RANDOM % 256)) $((RANDOM % 256)))

cat >/tmp/default.xml <<EOF
<network>
  <name>default</name>
  <uuid>$UUID</uuid>
  <forward mode='nat'/>
  <bridge name='virbr0' stp='on' delay='0'/>
  <mac address='$MAC'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='*************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

virsh net-define /tmp/default.xml
virsh net-autostart default
virsh net-start default
