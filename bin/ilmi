#!/usr/bin/env bash

# shellcheck disable=SC1090
source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)

# all options
# "sys_python" "core" "snap" "essential" "cli-slim" "cli" "cpp" "vscode_bin" "terminal_bin" "ui" "cockpit" "vm" "docker" "incus" "distrobox" "sway" "hyprland" "core" "sys_python" "essential" "cli" "cpp" "terminal_bin" "vm" "incus" "distrobox" "bash" "macos_settings" "vscode_bin" "terminal_bin" "ui" "core" "essential" "cli" "fonts" "cpp" "podman" "vm" "pyenv_mac" "emacs_bin" "docker" "core" "sys_python" "yay" "snap" "essential" "cli-slim" "cli" "cpp" "vscode_bin" "terminal_bin" "ui" "cockpit" "vm" "docker" "incus" "distrobox" "nix" "devbox" "nix-no-init" "emacs_bin" "go" "multipass" "vscode" "docker" "code_server" "coder" "pyenv" "tailscale" "portainer" "sway" "hyprland" "# kwin_script" "# home_manager" "gnome_keybindings" "# ulauncher" "pacstall" "core" "sys_python" "snap" "essential" "cli-slim" "cli" "cpp" "vscode_bin" "windsurf" "terminal_bin" "ui" "cockpit" "vm" "incus" "distrobox" "base_dev" "sys_python" "core" "essential" "cli-slim" "cli" "snap" "cpp" "vscode_bin" "hyprland_bin" "sway_bin" "brew" "mise_shell" "brew-shell-slim" "brew-shell" "pixi-shell-slim" "pixi-shell" "go_shell" "rust_shell" "npm_shell" "webi_shell" "miniconda" "vscode-extensions" "vscode-all-extensions" "poetry" "pyenv-anaconda" "pyenv-miniconda" "npm" "web" "go-tools" "devenv" "shell-slim" "shell" "webi" "mise" "pixi" "# pkgx" "uv" "python" "flathub" "kitty_bin" "kitty" "ghostty_bin" "ghostty" "atomic_distrobox" "cmake" "gnome-extensions" "gnome-settings" "gnome-flatpaks" "vscode_flatpak" "ptyxis" "apps-slim" "apps" "more_apps" "atomic_nvim" "micro" "rust" "conan" "monaspace" "cascadia_nerd_font" "monaspace_nerd_font" "jetbrains_nerd_font" "maple_font" "nerd_font" "fonts" "starship"

INSTALL_OPTIONS=(
    "shell-slim" "shell" "vscode" "fonts" "docker" "npm" "vm" "ptyxis" "apps"
    "incus" "nix" "brew" "pixi" "tailscale"
    "@nix" "@shell" "@vm" "@hyprland" "@sway"
)

DEFAULT_OPTIONS=("shell-slim" "vscode" "fonts")

help() {
    echo "Usage: $0 [OPTION1] [OPTION2] ..."
    echo "Options:"
    echo "  shell-slim   essential shell packages."
    echo "  shell        shell tools like zsh, tmux, nvim."
    echo "  vscode       vscode and extensions."
    echo "  fonts        nerd fonts for jetbrains mono, cascadia code mono."
    echo "  docker       docker only"
    echo "  npm          npm tools like pnpm, claude, gemini."
    echo "  vm           vm tools like distrobox, libvirt, virt-manager."
    echo "  ptyxis       ptyxis terminal."
    echo "  apps         flatpak apps like firefox, chromium, zoom, obsidian, wezterm."
    echo "  incus        incus and lxc."
    echo "  nix          nix installation"
    echo "  brew         homebrew for macos and linuxbrew for linux."
    echo "  pixi         pixi for managing global packages and modern conda for python."
    echo "  tailscale    tailscale for managing vpn."
    echo "  web          pnpm based web dev tools."
    echo "  python       python dev tools."
    echo "  cpp          gcc, clang, cmake, boost."
    echo "  mise         mise for managing tools like rust, go, python, etc."
    echo "  rust         rust dev tools."
    echo "  go           go dev tools."
    echo "  devbox       devbox for managing dev containers."
    echo "  help         show this help."
    echo ""
    echo "Group Options"
    echo "  @nix         nix installation"
    echo "  @shell       min and shell tools and config for zsh, tmux, neovim."
    echo "  @vm          vm tools like buildah, libvirt, virt-manager."
    echo "  @hyprland    hyprland desktop on fedora and tumbleweed."
    echo "  @sway        sway desktop on fedora and tumbleweed."
    echo "  help         show this help."
    echo ""
}

main() {
    if ! [[ "$#" -eq 0 ]]; then
        common-installer "$@"
        return 0
    fi

    if ! has_cmd gum; then
        help
        exit 0
    fi

    readarray -t SELECTED_OPTIONS < <(
        gum choose \
            --no-limit \
            --selected="$(
                IFS=,
                echo "${DEFAULT_OPTIONS[*]}"
            )" \
            --header "Choose options to install" \
            "${INSTALL_OPTIONS[@]}"
    )

    if [[ ${#SELECTED_OPTIONS[@]} -gt 0 ]]; then
        echo ""
        slog "Selected: ${SELECTED_OPTIONS[*]}. Installing..."
        echo ""
        common-installer "${SELECTED_OPTIONS[@]}"
        slog "Installation complete."
    else
        slog "No options selected."
        return 1
    fi

}

if [[ "$1" == "help" ]]; then
    help
else
    if [[ "$1" == "generic" ]] || [[ "$1" == "generic-ct" ]] || [[ "$1" == "fedora-atomic" ]] || is_multipass || is_distrobox; then
        export NOSUDO=1
    fi

    CLICOLOR_FORCE=1 bootstrap "$@"
fi
