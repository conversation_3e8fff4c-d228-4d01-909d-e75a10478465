#!/usr/bin/env bash

# shellcheck disable=SC1090
source <(curl -sSL https://raw.githubusercontent.com/pervezfunctor/dotfiles/refs/heads/main/share/utils)

INSTALL_OPTIONS=(
    "work"
    "shell-slim"
    "shell"
    "vscode"
    "terminal"
    "docker"
    "vm"
    "dev"
    "nix"
    "dev-nix"
    # "hyprland"
    # "sway"
)

help() {
    echo "Usage: $0 [OPTION]"
    echo "Options:"
    echo "  work            shell, vscode, docker and terminal app."
    echo "  shell-slim      essential shell packages."
    echo "  shell           shell, and terminal app."
    echo "  vscode          vscode and extensions."
    echo "  terminal        terminal app and nerd fonts."
    echo "  vm              libvirt, virt-manager and gnome-boxes."
    echo "  dev             shell-slim, vscode, libvirt, distrobox and terminal app."
    echo "  nix             nix installation with home-manager."
    echo "  dev-nix         nix installation with home-manager and vscode."
    echo "  help            shows this help."
    echo ""
}

main() {
    if ! [[ "$#" -eq 0 ]]; then
        if fn_exists "${1}_groupstall"; then
            "${1}_groupstall"
            return 0
        else
            err_exit "No such installer: $1"
        fi
    fi

    if ! has_cmd gum; then
        help
        exit 0
    fi

    local SELECTED_OPTION
    SELECTED_OPTION=$(gum choose --header "Choose option to install" "${INSTALL_OPTIONS[@]}")

    if [[ -n "$SELECTED_OPTION" ]]; then
        echo ""
        slog "Installing $SELECTED_OPTION..."
        echo ""
        "${SELECTED_OPTION}_mainstall"
        slog "Installation complete."
    else
        slog "No option selected."
        return 1
    fi
}

if [[ "$1" == "help" || "$1" == "-h" || "$1" == "--help" ]]; then
    help
else
    CLICOLOR_FORCE=1 bootstrap "$@"
fi
