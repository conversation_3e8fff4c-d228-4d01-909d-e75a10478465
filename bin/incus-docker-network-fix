#!/usr/bin/env bash
set -e

INCUS_SUBNET="************/24"

echo "[+] Enabling IPv4 forwarding..."
sudo sysctl -w net.ipv4.ip_forward=1
sudo bash -c 'echo "net.ipv4.ip_forward=1" > /etc/sysctl.d/99-incus-ipforward.conf'

echo "[+] Fixing iptables POSTROUTING MASQUERADE rule for Incus bridge..."
# Remove existing rule if present
sudo iptables -t nat -D POSTROUTING -s "$INCUS_SUBNET" -j MASQUERADE 2>/dev/null || true
# Add clean MASQUERADE rule
sudo iptables -t nat -A POSTROUTING -s "$INCUS_SUBNET" -j MASQUERADE

echo "[+] Setting iptables FORWARD policy to ACCEPT..."
sudo iptables -P FORWARD ACCEPT

echo "[✓] Incus networking fixed. Containers should now have internet access."
