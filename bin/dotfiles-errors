#!/usr/bin/env bash

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

SEARCHES=(
  "denied"
  "fail"
  "warn"
  "error"
  "not"
  "unable"
  "denied"
  "fatal"
  "Unknown"
  "No match"
)

for search in "${SEARCHES[@]}"; do
  if grep -i "$search" <~/.dotfiles-output.log; then
    press_enter
  fi
done

for search in "${SEARCHES[@]}"; do
  if grep -i "$search" <~/.dotfiles-error.log; then
    press_enter
  fi
done
