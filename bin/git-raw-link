#!/usr/bin/env bash

set -euo pipefail

usage() {
    echo "Usage: $0 <file-path>"
    echo "  <file-path>: Path to a file in the Git repository"
    exit 1
}

if [[ $# -ne 1 ]]; then
    usage
fi

FILE_PATH="$1"

# Ensure we're in a git repo
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "Error: Not in a Git repository." >&2
    exit 1
fi

# Get absolute path of the file
if [[ ! -e "$FILE_PATH" ]]; then
    echo "Error: File '$FILE_PATH' does not exist." >&2
    exit 1
fi

ABS_FILE_PATH="$(cd "$(dirname "$FILE_PATH")" && pwd -P)/$(basename "$FILE_PATH")"

# Get repo root (absolute path)
REPO_ROOT="$(git rev-parse --show-toplevel)"

# Compute relative path from repo root
if [[ "$ABS_FILE_PATH" == "$REPO_ROOT"* ]]; then
    REL_FILE_PATH="${ABS_FILE_PATH#"${REPO_ROOT}/"}"
else
    echo "Error: File is not inside the Git repository." >&2
    exit 1
fi

# Optional: Verify it's not outside due to symlinks (basic safety)
if [[ "$REL_FILE_PATH" == "$ABS_FILE_PATH" ]]; then
    echo "Error: Failed to compute repo-relative path." >&2
    exit 1
fi

# Get remote URL
REMOTE_URL=$(git config --get remote.origin.url || git remote get-url --all | head -n1)
if [[ -z "$REMOTE_URL" ]]; then
    echo "Error: No remote repository configured." >&2
    exit 1
fi

# Clean and parse remote URL
REMOTE_URL_CLEAN="${REMOTE_URL%.git}"
REMOTE_URL_CLEAN="${REMOTE_URL_CLEAN#git@}"
REMOTE_URL_CLEAN="${REMOTE_URL_CLEAN#https://}"
REMOTE_URL_CLEAN="${REMOTE_URL_CLEAN/://}"

# Extract host, owner, repo
if [[ "$REMOTE_URL_CLEAN" =~ ^([^/]+)/([^/]+)/([^/]+)$ ]]; then
    HOST="${BASH_REMATCH[1]}"
    OWNER="${BASH_REMATCH[2]}"
    REPO="${BASH_REMATCH[3]}"
elif [[ "$REMOTE_URL_CLEAN" =~ ^([^/]+):([^/]+)/([^/]+)$ ]]; then
    HOST="${BASH_REMATCH[1]}"
    OWNER="${BASH_REMATCH[2]}"
    REPO="${BASH_REMATCH[3]}"
else
    echo "Error: Could not parse remote URL: $REMOTE_URL" >&2
    exit 1
fi

# Get current branch or tag
REF=$(git symbolic-ref --short HEAD 2>/dev/null || \
      git describe --tags --exact-match 2>/dev/null || \
      echo "HEAD")

# URL-encode the path (basic)
ENCODED_PATH=$(printf '%s' "$REL_FILE_PATH" | sed 's/%/%25/g; s/ /%20/g; s/#/%23/g; s/?/%3F/g')

# Construct raw URL
case "$HOST" in
    "github.com")
        RAW_URL="https://raw.githubusercontent.com/$OWNER/$REPO/$REF/$ENCODED_PATH"
        ;;
    "gitlab.com"|"dev.azure.com"|"visualstudio.com")
        if [[ "$HOST" == "dev.azure.com" ]]; then
            IFS='/' read -ra PARTS <<< "$REPO"
            if [[ ${#PARTS[@]} -ge 3 ]]; then
                PROJECT="${PARTS[0]}"
                AZ_REPO="${PARTS[2]}"
                RAW_URL="https://dev.azure.com/$OWNER/$PROJECT/_apis/git/repositories/$AZ_REPO/items?path=/$ENCODED_PATH&versionType=branch&version=$REF&format=raw"
            else
                echo "Error: Unsupported Azure DevOps repo format." >&2
                exit 1
            fi
        elif [[ "$HOST" == "visualstudio.com" ]]; then
            RAW_URL="https://$OWNER.visualstudio.com/$REPO/_apis/git/repositories/$REPO/items?path=/$ENCODED_PATH&versionType=branch&version=$REF&format=raw"
        else
            RAW_URL="https://$HOST/$OWNER/$REPO/-/raw/$REF/$ENCODED_PATH"
        fi
        ;;
    "bitbucket.org")
        RAW_URL="https://$HOST/$OWNER/$REPO/raw/$REF/$ENCODED_PATH"
        ;;
    *)
        RAW_URL="https://$HOST/$OWNER/$REPO/-/raw/$REF/$ENCODED_PATH"
        ;;
esac

echo "$RAW_URL"
