#! /usr/bin/env bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}
# shellcheck disable=SC1091
source "${DOT_DIR}/share/utils"

choose() {
  local selected_csv="$1"
  shift
  local -a install_options=("$@")

  [[ ${#install_options[@]} -eq 0 ]] && return 1

  local tool
  if has_cmd dialog; then
    tool="dialog"
  elif has_cmd whiptail; then
    tool="whiptail"
  else
    fail "Neither dialog nor whiptail is available"
    return 1
  fi

  local checklist_items=()
  for option in "${install_options[@]}"; do
    if [[ ",$selected_csv," == *",$option,"* ]]; then
      checklist_items+=("$option" "" "ON")
    else
      checklist_items+=("$option" "" "OFF")
    fi
  done

  # Calculate dialog size
  local num_options=${#install_options[@]}
  local list_height=$((num_options > 10 ? 10 : num_options))
  local dialog_height=$((list_height + 10))

  "$tool" --title "Choose Options" \
    --checklist "Choose options to install" "$dialog_height" 78 "$list_height" \
    "${checklist_items[@]}" \
    3>&1 1>&2 2>&3 || return 1
}

# example() {
#   INSTALL_OPTIONS=("nginx" "docker" "nodejs" "git" "vim")
#   SELECTED_CSV="docker,git"

#   local selection selected
#   if selection=$(choose "$SELECTED_CSV" "${INSTALL_OPTIONS[@]}"); then
#     printf '\033[0m\033[?25h'
#     tput sgr0 2>/dev/null || true
#     read -r -a selected <<<"$selection"
#     echo "Selected ${#selected[@]} options:"
#     printf " - %s\n" "${selected[@]}"
#   else
#     echo "Selection cancelled"
#   fi
# }

# example
