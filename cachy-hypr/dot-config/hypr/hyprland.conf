# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                 CachyOS Hyprland Configuration              ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

$mainMod = SUPER

# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                         Source Files                        ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
source = ~/.config/hypr/config/animations.conf
source = ~/.config/hypr/config/autostart.conf
source = ~/.config/hypr/config/decorations.conf
source = ~/.config/hypr/config/environment.conf
source = ~/.config/hypr/config/input.conf
source = ~/.config/hypr/config/keybinds.conf
source = ~/.config/hypr/config/monitor.conf
source = ~/.config/hypr/config/variables.conf
source = ~/.config/hypr/config/windowrules.conf

# Modifying these configs can be done by creating a user defined config in the home directory, e.g.
## ~/.config/hypr/config/user-config.conf
# source = ~/.config/hypr/config/user-config.conf
