
# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                         Keybinds                            ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

source = ~/.config/hypr/config/defaults.conf

# https://wiki.hyprland.org/Configuring/Binds/
bindd = $mainMod, RETURN, Opens your preferred terminal emulator ($terminal), exec, $terminal
bindd = $mainMod, E, Opens your preferred filemanager ($filemanager), exec, $filemanager
bindd = $mainMod, Q, Closes (not kill) current window, killactive,
bindd = $mainMod SHIFT, M, Exits Hyprland by terminating the user sessions, exec, loginctl terminate-user ""
bindd = $mainMod, V, Switches current window between floating and tiling mode, togglefloating,
bindd = $mainMod, SPACE, Runs your application launcher, exec, $applauncher
bindd = $mainMod, F, Toggles current window fullscreen mode, fullscreen
bindd = $mainMod, Y, Pin current window (shows on all workspaces), pin
bindd = $mainMod, J, Toggles current window split mode, togglesplit, # dwindle

bindd = , Print, Creates a screenshot of an area, exec, $shot-region
bindd = CTRL, Print, Creates a screenshot of the active window, exec, $shot-window
bindd = ALT, Print, Creates a screenshot of the active display, exec, $shot-screen

# ======= Grouping Windows =======

bindd = $mainMod, K, Toggles  current window group mode (ungroup all related), togglegroup,
bindd = $mainMod, Tab, Switches to the next window in the group, changegroupactive, f

# ======= Toggle Gaps =======

bindd = $mainMod SHIFT, G, Set CachyOS default gaps, exec, hyprctl --batch "keyword general:gaps_out 5;keyword general:gaps_in 3"
bindd = $mainMod, G, Remove gaps between window, exec, hyprctl --batch "keyword general:gaps_out 0;keyword general:gaps_in 0"

# ======= Volume Control =======

bindel = , XF86AudioRaiseVolume, exec, pactl set-sink-volume @DEFAULT_SINK@ +5% && pactl get-sink-volume @DEFAULT_SINK@ | grep -oP '\d+(?=%)' | awk '{if($1>100) system("pactl set-sink-volume @DEFAULT_SINK@ 100%")}' && pactl get-sink-volume @DEFAULT_SINK@ | grep -oP '\d+(?=%)' | awk '{print $1}' | head -1 > /tmp/$HYPRLAND_INSTANCE_SIGNATURE.wob # Raise Volume
bindel = , XF86AudioLowerVolume, exec, pactl set-sink-volume @DEFAULT_SINK@ -5% && pactl get-sink-volume @DEFAULT_SINK@ | grep -oP '\d+(?=%)' | awk '{print $1}' | head -1 > /tmp/$HYPRLAND_INSTANCE_SIGNATURE.wob # Lower Volume
bindel = , XF86AudioMute, exec, amixer sset Master toggle | sed -En '/\[on\]/ s/.*\[([0-9]+)%\].*/\1/ p; /\[off\]/ s/.*/0/p' | head -1 > /tmp/$HYPRLAND_INSTANCE_SIGNATURE.wob	#Mutes player audio

# ======= Playback Control =======

bindd = , XF86AudioPlay, Toggles play/pause, exec, playerctl play-pause
bindd = , XF86AudioNext, Next track, exec, playerctl next
bindd = , XF86AudioPrev, Previous track, exec, playerctl previous

# ======= Screen Brightness =======

bindel = , XF86MonBrightnessUp, exec, brightnessctl s +5% 	#Increases brightness 5%
bindel = , XF86MonBrightnessDown, exec, brightnessctl s 5%- 	#Decreases brightness 5%
bindd = $mainMod, L, Lock the screen, exec, swaylock-fancy -e -K -p 10 -f Hack-Regular
bindd = $mainMod, O, Reload/restarts Waybar, exec, killall -SIGUSR2 waybar

# ======= Window Actions =======

## Move window with mainMod + LMB/RMB and dragging
bindd = $mainMod, mouse:272, Move the window towards a direction, movewindow
## Move window towards a direction
bindd = $mainMod SHIFT, left, Move active window to the left, movewindow, l
bindd = $mainMod SHIFT, right, Move active window to the right, movewindow, r
bindd = $mainMod SHIFT, up, Move active window upwards, movewindow, u
bindd = $mainMod SHIFT, down, Move active window downwards, movewindow, d
## Move focus with mainMod + arrow keys
bindd = $mainMod, left, Move focus to the left, movefocus, l
bindd = $mainMod, right,  Move focus to the right, movefocus, r
bindd = $mainMod, up, Move focus upwards, movefocus, u
bindd = $mainMod, down, Move focus downwards, movefocus, d

## Resizing windows
# Activate keyboard window resize mode
# https://wiki.hyprland.org/Configuring/Binds/#submaps
bindd = $mainMod, R, Activates window resizing mode, submap, resize
submap = resize
bindd = , right, Resize to the right (resizing mode), resizeactive, 15 0
bindd = , left, Resize to the left (resizing mode), resizeactive, -15 0
bindd = , up, Resize upwards (resizing mode), resizeactive, 0 -15
bindd = , down, Resize downwards (resizing mode), resizeactive, 0 15
bindd = , l, Resize to the right (resizing mode), resizeactive, 15 0
bindd = , h, Resize to the left (resizing mode), resizeactive, -15 0
bindd = , k, Resize upwards (resizing mode), resizeactive, 0 -15
bindd = , j, Resize downwards (resizing mode), resizeactive, 0 15
bindd = , escape, Ends window resizing mode, submap, reset
submap = reset
# Quick resize window with keyboard
# !!! added $mainMod here because CTRL + SHIFT is used for word selection in various text editors
bindd = $mainMod CTRL SHIFT, right, Resize to the right, resizeactive, 15 0
bindd = $mainMod CTRL SHIFT, left, Resize to the left, resizeactive, -15 0
bindd = $mainMod CTRL SHIFT, up, Resize upwards, resizeactive, 0 -15
bindd = $mainMod CTRL SHIFT, down, Resize downwards, resizeactive, 0 15
bindd = $mainMod CTRL SHIFT, l, Resize to the right, resizeactive, 15 0
bindd = $mainMod CTRL SHIFT, h, Resize to the left, resizeactive, -15 0
bindd = $mainMod CTRL SHIFT, k, Resize upwards, resizeactive, 0 -15
bindd = $mainMod CTRL SHIFT, j, Resize downwards, resizeactive, 0 15
# Resize window with mainMod + LMB/RMB and dragging
bindm = $mainMod, mouse:273, resizewindow	#Resize the window towards a direction
bindm = $mainMod, mouse:272, movewindow		#Drag window
## Resizing Windows End #

## Move active window to a workspace with $mainMod + CTRL + [0-9]
bindd = $mainMod CTRL, 1, Move window and switch to workspace 1, movetoworkspace, 1
bindd = $mainMod CTRL, 2, Move window and switch to workspace 2, movetoworkspace, 2
bindd = $mainMod CTRL, 3, Move window and switch to workspace 3, movetoworkspace, 3
bindd = $mainMod CTRL, 4, Move window and switch to workspace 4, movetoworkspace, 4
bindd = $mainMod CTRL, 5, Move window and switch to workspace 5, movetoworkspace, 5
bindd = $mainMod CTRL, 6, Move window and switch to workspace 6, movetoworkspace, 6
bindd = $mainMod CTRL, 7, Move window and switch to workspace 7, movetoworkspace, 7
bindd = $mainMod CTRL, 8, Move window and switch to workspace 8, movetoworkspace, 8
bindd = $mainMod CTRL, 9, Move window and switch to workspace 9, movetoworkspace, 9
bindd = $mainMod CTRL, 0, Move window and switch to workspace 10, movetoworkspace, 10
bindd = $mainMod CTRL, left, Move window and switch to the next workspace, movetoworkspace, -1
bindd = $mainMod CTRL, right, Move window and switch to the previous workspace, movetoworkspace, +1
## Same as above, but doesn't switch to the workspace
bindd = $mainMod SHIFT, 1, Move window silently to workspace 1, movetoworkspacesilent, 1
bindd = $mainMod SHIFT, 2, Move window silently to workspace 2, movetoworkspacesilent, 2
bindd = $mainMod SHIFT, 3, Move window silently to workspace 3, movetoworkspacesilent, 3
bindd = $mainMod SHIFT, 4, Move window silently to workspace 4, movetoworkspacesilent, 4
bindd = $mainMod SHIFT, 5, Move window silently to workspace 5, movetoworkspacesilent, 5
bindd = $mainMod SHIFT, 6, Move window silently to workspace 6, movetoworkspacesilent, 6
bindd = $mainMod SHIFT, 7, Move window silently to workspace 7, movetoworkspacesilent, 7
bindd = $mainMod SHIFT, 8, Move window silently to workspace 8, movetoworkspacesilent, 8
bindd = $mainMod SHIFT, 9, Move window silently to workspace 9, movetoworkspacesilent, 9
bindd = $mainMod SHIFT, 0, Move window silently to workspace 10, movetoworkspacesilent, 10
# Window actions End #

# ======= Workspace Actions =======

# Switch workspaces with mainMod + [0-9]
bindd = $mainMod, 1, Switch to workspace 1, workspace, 1
bindd = $mainMod, 2, Switch to workspace 2, workspace, 2
bindd = $mainMod, 3, Switch to workspace 3, workspace, 3
bindd = $mainMod, 4, Switch to workspace 4, workspace, 4
bindd = $mainMod, 5, Switch to workspace 5, workspace, 5
bindd = $mainMod, 6, Switch to workspace 6, workspace, 6
bindd = $mainMod, 7, Switch to workspace 7, workspace, 7
bindd = $mainMod, 8, Switch to workspace 8, workspace, 8
bindd = $mainMod, 9, Switch to workspace 9, workspace, 9
bindd = $mainMod, 0, Switch to workspace 10, workspace, 10
# Scroll through existing workspaces with mainMod + , or .
bindd = $mainMod, PERIOD, Scroll through workspaces incrementally, workspace, e+1
bindd = $mainMod, COMMA, Scroll through workspaces decrementally, workspace, e-1
# With $mainMod + scroll
bindd = $mainMod, mouse_down, Scroll through workspaces incrementally, workspace, e+1
bindd = $mainMod, mouse_up, Scroll through workspaces decrementally, workspace, e-1
bindd = $mainMod, slash, Switch to the previous workspace, workspace, previous
# Special workspaces (scratchpads)
bindd = $mainMod, minus, Move active window to Special workspace, movetoworkspace, special
bindd = $mainMod, equal, Toggles the Special workspace, togglespecialworkspace, special
bindd = $mainMod, F1, Call special workspace scratchpad, togglespecialworkspace, scratchpad
bindd = $mainMod ALT SHIFT, F1, Move active window to special workspace scratchpad, movetoworkspacesilent, special:scratchpad

# ======= Additional Settings =======

# https://wiki.hyprland.org/Configuring/Binds
binds {
    allow_workspace_cycles = 1
    workspace_back_and_forth = 1
    workspace_center_on = 1
	movefocus_cycles_fullscreen = true
	window_direction_monitor_fallback = true
}
