# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                    Autostart Configuration                  ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

source = ~/.config/hypr/config/defaults.conf

# Autostart wiki https://wiki.hyprland.org/Configuring/Keywords/#executing

exec-once = swaybg -o \* -i /usr/share/wallpapers/cachyos-wallpapers/Skyscraper.png -m fill
exec-once = waybar &
exec-once = fcitx5 -d &
exec-once = mako &
exec-once = nm-applet --indicator &
exec-once = bash -c "mkfifo /tmp/$HYPRLAND_INSTANCE_SIGNATURE.wob && tail -f /tmp/$HYPRLAND_INSTANCE_SIGNATURE.wob | wob & disown" &
exec-once = /usr/lib/polkit-kde-authentication-agent-1 &

# ## Slow app launch fix
exec-once = systemctl --user import-environment &
exec-once = hash dbus-update-activation-environment 2>/dev/null &
exec-once = dbus-update-activation-environment --systemd &

# ## Idle configuration
# exec-once = $idlehandler
exec-once = hypridle

exec-once = sh -c 'eval $(gnome-keyring-daemon --start --components=pkcs11,secrets,ssh) > ~/.cache/gnome-keyring.log 2>&1'
