{ pkgs, ... }:
{
  programs.zsh.enable = true;

  environment.systemPackages = with pkgs; [
    alejandra
    bash
    coreutils
    curl
    dialog
    ethtool
    fio
    fuse2
    gawk
    gcc
    git
    glibc
    gnugrep
    gnumake
    iproute2
    lefthook
    libsecret
    lm_sensors
    lsof
    micro
    net-tools
    newt
    nixd
    nixfmt-rfc-style
    nmap
    numactl
    nvme-cli
    pciutils
    plocate
    proxmox-backup-client
    rclone
    restic
    rsync
    smartmontools
    statix
    stress
    tcpdump
    udisks2
    unzip
    usbutils
    vim
    wget
    zfs
    zfstools
    zip
    zstd
  ];
}
