# Do not modify this file!  It was generated by ‘nixos-generate-config’
# and may be overwritten by future invocations.  Please make changes
# to /etc/nixos/configuration.nix instead.
{ config, lib, pkgs, modulesPath, ... }:

{
  imports =
    [ (modulesPath + "/installer/scan/not-detected.nix")
    ];

  boot.initrd.availableKernelModules = [ "nvme" "xhci_pci" "usbhid" ];
  boot.initrd.kernelModules = [ ];
  boot.kernelModules = [ "kvm-amd" ];
  boot.extraModulePackages = [ ];

  fileSystems."/" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@" ];
    };

  fileSystems."/nix" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@nix" ];
    };

  fileSystems."/var/log" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@log" ];
    };

  fileSystems."/.snapshots" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@snapshots" ];
    };

  fileSystems."/home" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@home" ];
    };

  fileSystems."/opt" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@opt" ];
    };

  fileSystems."/srv" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@srv" ];
    };

  fileSystems."/tmp" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@tmp" ];
    };

  fileSystems."/var/cache" =
    { device = "/dev/disk/by-uuid/ce2b4bf9-771d-4263-b7c0-6732f4445ab4";
      fsType = "btrfs";
      options = [ "subvol=@cache" ];
    };

  fileSystems."/boot" =
    { device = "/dev/disk/by-uuid/CA5C-7FCF";
      fsType = "vfat";
      options = [ "fmask=0077" "dmask=0077" ];
    };

  swapDevices =
    [ { device = "/dev/disk/by-uuid/72a44dee-6b86-4582-bacb-af14f51f0c79"; }
    ];

  nixpkgs.hostPlatform = lib.mkDefault "x86_64-linux";
  hardware.cpu.amd.updateMicrocode = lib.mkDefault config.hardware.enableRedistributableFirmware;
}
