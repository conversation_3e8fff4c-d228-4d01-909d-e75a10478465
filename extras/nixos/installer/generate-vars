#! /usr/bin/env bash

set -euo pipefail

info() {
  printf -- "--> %s\n" "$1"
}

error() {
  printf -- "❌ ERROR: %s\n" "$1" >&2
  exit 1
}

generate_vars_nix() {
  info "Generating vars.nix..."

  while [[ $# -gt 0 ]]; do
    case "$1" in
    --user-name)
      USER_NAME="$2"
      shift 2
      ;;
    --initial-password)
      INITIAL_PASSWORD="$2"
      shift 2
      ;;
    --disk)
      DISK="$2"
      shift 2
      ;;
    --swap-size)
      SWAP_SIZE="$2"
      shift 2
      ;;
    --hostname)
      HOSTNAME="$2"
      shift 2
      ;;
    *)
      error "Unknown option: $1"
      ;;
    esac
  done

  if [[ -z "$USER_NAME" ]]; then
    USER_NAME="me"
  fi

  if [[ -z "$INITIAL_PASSWORD" ]]; then
    INITIAL_PASSWORD="nixos"
  fi

  if [[ -z "$DISK" ]]; then
    error "Missing required --disk argument."
  fi

  if [[ -z "$SWAP_SIZE" ]]; then
    SWAP_SIZE="8G"
  fi

  if [[ -z "$HOSTNAME" ]]; then
    error "Missing required --hostname argument."
  fi

  if [[ ! -f ~/.ssh/id_ed25519.pub ]]; then
    error "SSH key not found. Please generate one with $(ssh-keygen -t ed25519)."
  fi

  local ssh_key
  ssh_key="$(cat ~/.ssh/id_ed25519.pub)"

  local vars_file="vars.nix"
  echo "{ pkgs, ... }:
  {
  hostName = \"${HOSTNAME}\";
  username = \"${USER_NAME}\";
  initialPassword = \"${INITIAL_PASSWORD}\";
  sshKey = \"${ssh_key}\";
  shell = pkgs.zsh;
  diskoMainDisk = \"${DISK}\";
  diskoSwapSize = \"${SWAP_SIZE}\";
}" >"${vars_file}"

  info "Generated ${vars_file}"
}

generate_vars_nix "$@"
