{ config, pkgs, modulesPath, lib, ... }:

let
  tftpRoot = "/var/lib/tftpboot";
  isoMount = "/mnt/isos";
  sambaPath = "//************/isos";
in {
  imports = [ (modulesPath + "/profiles/qemu-guest.nix") ];

  # _module.args.proxmox.enable = true;

  services.qemuGuest.enable = lib.mkDefault true;
  networking.hostName = "pxeserver";
  networking.interfaces.enp1s0.ipv4.addresses = [{
    address = "*************";
    prefixLength = 24;
  }];
  networking.defaultGateway = "***********";
  networking.nameservers = [ "*******" "*******" ];

  fileSystems."${isoMount}" = {
    device = sambaPath;
    fsType = "cifs";
    options = [ "guest" "rw" "uid=0" "gid=0" "vers=3.0" ];
  };

  services.nginx = {
    enable = true;
    virtualHosts."_" = {
      root = tftpRoot;
      extraConfig = "autoindex on;";
    };
  };

  services.dnsmasq = {
    enable = true;
    enableTFTP = true;
    enableProxyDhcp = true;
    extraConfig = ''
      interface=enp1s0
      bind-interfaces
      dhcp-range=***********,proxy
      dhcp-match=set:ipxe,175
      dhcp-boot=tag:!ipxe,ipxe.efi
      dhcp-boot=tag:ipxe,http://*************/ipxe/pxe-menu.ipxe
      tftp-root=${tftpRoot}
    '';
  };

  networking.firewall.allowedTCPPorts = [ 80 ];
  networking.firewall.allowedUDPPorts = [ 69 4011 ];

  environment.systemPackages = with pkgs; [ syslinux ipxe wget ];

  systemd.tmpfiles.rules = [
    "L+ ${tftpRoot}/iso - - - - ${isoMount}"
    "L+ ${tftpRoot}/ipxe - - - - ${tftpRoot}/ipxe"
  ];

  system.activationScripts.setupPXE.text = ''
    set -e
    mkdir -p ${tftpRoot}/ipxe

    cp ${pkgs.ipxe}/ipxe.efi ${tftpRoot}/ipxe/
    cp ${pkgs.syslinux}/share/syslinux/ldlinux.c32 ${tftpRoot}/
    cp ${pkgs.syslinux}/share/syslinux/lib*.c32 ${tftpRoot}/

    cp ${./pxe-menu.ipxe} ${tftpRoot}/ipxe/pxe-menu.ipxe
  '';

  security.sudo.wheelNeedsPassword = false;

  services.openssh = {
    enable = true;
    settings.PasswordAuthentication = false;
    settings.KbdInteractiveAuthentication = false;
  };
  programs.ssh.startAgent = true;

  users.users.me = {
    isNormalUser = true;
    description = "Your Name";
    extraGroups = [ "networkmanager" "wheel" ];
  };

  users.users.me.openssh.authorizedKeys.keys = [''
    ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIIcXIDK5n+AIXExMo9nt1PRGcowyvyZUPvhBGRJRGMAl pervez@fedora
  ''];
  system.stateVersion = "25.11";
}
