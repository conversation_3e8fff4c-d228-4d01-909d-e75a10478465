#! /usr/bin/env bash

set -euo pipefail

main() {
  local remote-ip="$1"
  local hostname="$2"

  mkdir -p "$HOME/.ilm/extras/nixos/config/hosts/${hostname}"
  nix run github:nix-community/nixos-anywhere -- --flake "$HOME"/.ilm/extras/nixos/config#"${hostname}" \
    --generate-hardware-config nixos-generate-config ./hosts/"${hostname}"/hardware-configuration.nix \
    --target-host "root@${remote-ip}"
}

if [[ $# -ne 2 ]]; then
  echo "Usage: $0 <remote-host-ip> <remote-hostname>"
  exit 1
fi

main "$@"
