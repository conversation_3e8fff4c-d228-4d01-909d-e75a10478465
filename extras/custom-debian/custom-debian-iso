#!/bin/bash

# Fedora 42 Custom ISO Builder with Btr<PERSON> and Snapper
# Usage: ./build-fedora42-custom-iso.sh [method]
# Methods: mkksiso (default), livemedia-creator, manual

set -e

# Configuration
FEDORA_VERSION="42"
KICKSTART_FILE="fedora42-btrfs-snapper.ks"
INPUT_ISO="Fedora-${FEDORA_VERSION}-x86_64-netinst.iso"
OUTPUT_ISO="Fedora-${FEDORA_VERSION}-Custom-Btrfs-Snapper.iso"
WORK_DIR="$(pwd)/iso_work"
BUILD_METHOD="${1:-mkksiso}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
  print_status "Checking dependencies..."

  local deps=()
  case $BUILD_METHOD in
  "mkksiso")
    deps=("mkksiso" "curl" "sha256sum")
    ;;
  "livemedia-creator")
    deps=("livemedia-creator" "curl" "sha256sum")
    ;;
  "manual")
    deps=("xorriso" "curl" "sha256sum" "unsquashfs" "mksquashfs")
    ;;
  esac

  for dep in "${deps[@]}"; do
    if ! command -v "$dep" &>/dev/null; then
      print_error "Missing dependency: $dep"
      case $dep in
      "mkksiso" | "livemedia-creator")
        echo "Install with: sudo dnf install lorax"
        ;;
      "xorriso")
        echo "Install with: sudo dnf install xorriso"
        ;;
      "unsquashfs" | "mksquashfs")
        echo "Install with: sudo dnf install squashfs-tools"
        ;;
      esac
      exit 1
    fi
  done

  print_success "All dependencies found"
}

download_fedora_iso() {
  if [[ ! -f "$INPUT_ISO" ]]; then
    print_status "Downloading Fedora $FEDORA_VERSION netinst ISO..."

    # Download from official mirror
    local iso_url="https://download.fedoraproject.org/pub/fedora/linux/releases/${FEDORA_VERSION}/Server/x86_64/iso/Fedora-Server-netinst-x86_64-${FEDORA_VERSION}-1.1.iso"

    if ! curl -L -o "$INPUT_ISO" "$iso_url"; then
      print_error "Failed to download Fedora ISO"
      print_warning "Please manually download the Fedora $FEDORA_VERSION netinst ISO and place it as: $INPUT_ISO"
      exit 1
    fi

    print_success "ISO downloaded successfully"
  else
    print_status "Using existing ISO: $INPUT_ISO"
  fi
}

validate_kickstart() {
  if [[ ! -f "$KICKSTART_FILE" ]]; then
    print_error "Kickstart file not found: $KICKSTART_FILE"
    print_warning "Please create the kickstart file first"
    exit 1
  fi

  print_status "Validating kickstart file..."

  # Basic validation
  if ! grep -q "^#version=" "$KICKSTART_FILE"; then
    print_warning "Kickstart file may be missing version declaration"
  fi

  if ! grep -q "btrfs" "$KICKSTART_FILE"; then
    print_warning "Kickstart file may not contain Btrfs configuration"
  fi

  print_success "Kickstart file validation completed"
}

build_with_mkksiso() {
  print_status "Building custom ISO using mkksiso method..."

  # Clean up previous builds
  [[ -f "$OUTPUT_ISO" ]] && rm -f "$OUTPUT_ISO"

  # Create custom ISO with embedded kickstart
  mkksiso --ks "$KICKSTART_FILE" "$INPUT_ISO" "$OUTPUT_ISO"

  if [[ $? -eq 0 && -f "$OUTPUT_ISO" ]]; then
    print_success "Custom ISO created: $OUTPUT_ISO"
    print_status "ISO size: $(du -h "$OUTPUT_ISO" | cut -f1)"
  else
    print_error "Failed to create custom ISO"
    exit 1
  fi
}

build_with_livemedia_creator() {
  print_status "Building custom ISO using livemedia-creator method..."

  # Clean up previous builds
  [[ -f "$OUTPUT_ISO" ]] && rm -f "$OUTPUT_ISO"
  [[ -d "$WORK_DIR" ]] && rm -rf "$WORK_DIR"

  mkdir -p "$WORK_DIR"

  # Create custom ISO
  livemedia-creator \
    --ks "$KICKSTART_FILE" \
    --iso "$INPUT_ISO" \
    --no-virt \
    --resultdir "$WORK_DIR" \
    --project "Fedora-Custom" \
    --make-iso \
    --volid "Fedora-${FEDORA_VERSION}-Custom" \
    --iso-name "$OUTPUT_ISO" \
    --releasever "$FEDORA_VERSION"

  # Move result to current directory
  if [[ -f "$WORK_DIR/$OUTPUT_ISO" ]]; then
    mv "$WORK_DIR/$OUTPUT_ISO" .
    print_success "Custom ISO created: $OUTPUT_ISO"
    print_status "ISO size: $(du -h "$OUTPUT_ISO" | cut -f1)"
  else
    print_error "Failed to create custom ISO"
    exit 1
  fi

  # Clean up work directory
  rm -rf "$WORK_DIR"
}

build_manual() {
  print_status "Building custom ISO using manual method..."

  # Clean up previous builds
  [[ -f "$OUTPUT_ISO" ]] && rm -f "$OUTPUT_ISO"
  [[ -d "$WORK_DIR" ]] && rm -rf "$WORK_DIR"

  mkdir -p "$WORK_DIR"/{original,custom}

  # Extract original ISO
  print_status "Extracting original ISO..."
  xorriso -osirrox on -indev "$INPUT_ISO" -extract / "$WORK_DIR/original/"

  # Copy to custom directory
  cp -r "$WORK_DIR/original/"* "$WORK_DIR/custom/"

  # Add kickstart file
  cp "$KICKSTART_FILE" "$WORK_DIR/custom/ks.cfg"

  # Modify isolinux.cfg to use kickstart
  if [[ -f "$WORK_DIR/custom/isolinux/isolinux.cfg" ]]; then
    sed -i 's/append initrd=initrd.img/append initrd=initrd.img ks=cdrom:\/ks.cfg/' "$WORK_DIR/custom/isolinux/isolinux.cfg"
  fi

  # Modify GRUB configuration
  if [[ -f "$WORK_DIR/custom/EFI/BOOT/grub.cfg" ]]; then
    sed -i 's/quiet/quiet ks=cdrom:\/ks.cfg/' "$WORK_DIR/custom/EFI/BOOT/grub.cfg"
  fi

  # Create new ISO
  print_status "Creating custom ISO..."
  xorriso -as mkisofs \
    -V "Fedora-${FEDORA_VERSION}-Custom" \
    -r -J -joliet-long \
    -b isolinux/isolinux.bin \
    -c isolinux/boot.cat \
    -no-emul-boot \
    -boot-load-size 4 \
    -boot-info-table \
    -eltorito-alt-boot \
    -e images/efiboot.img \
    -no-emul-boot \
    -o "$OUTPUT_ISO" \
    "$WORK_DIR/custom/"

  if [[ $? -eq 0 && -f "$OUTPUT_ISO" ]]; then
    print_success "Custom ISO created: $OUTPUT_ISO"
    print_status "ISO size: $(du -h "$OUTPUT_ISO" | cut -f1)"
  else
    print_error "Failed to create custom ISO"
    exit 1
  fi

  # Clean up work directory
  rm -rf "$WORK_DIR"
}

test_iso() {
  print_status "Basic ISO validation..."

  if [[ ! -f "$OUTPUT_ISO" ]]; then
    print_error "Output ISO not found"
    return 1
  fi

  # Check if ISO is readable
  if file "$OUTPUT_ISO" | grep -q "ISO 9660"; then
    print_success "ISO format validation passed"
  else
    print_error "Invalid ISO format"
    return 1
  fi

  # Check ISO size (should be reasonable)
  local size
  size=$(stat -f%z "$OUTPUT_ISO" 2>/dev/null || stat -c%s "$OUTPUT_ISO" 2>/dev/null)
  if [[ $size -gt 100000000 ]]; then # > 100MB
    print_success "ISO size looks reasonable: $(numfmt --to=iec "$size")"
  else
    print_warning "ISO size seems small: $(numfmt --to=iec "$size")"
  fi
}

show_usage() {
  cat <<EOF
Fedora 42 Custom ISO Builder with Btrfs and Snapper

Usage: $0 [method]

Available methods:
  mkksiso          - Use mkksiso tool (recommended, fastest)
  livemedia-creator - Use livemedia-creator (comprehensive)
  manual           - Manual ISO extraction and rebuilding

Requirements:
  - Fedora/RHEL/CentOS system with development tools
  - lorax package (for mkksiso and livemedia-creator)
  - xorriso and squashfs-tools (for manual method)
  - Internet connection to download base ISO

The script will:
1. Download Fedora $FEDORA_VERSION netinst ISO if needed
2. Validate the kickstart configuration
3. Build custom ISO with embedded kickstart
4. Perform basic validation of the result

The resulting ISO will install Fedora with:
- Btrfs filesystem with optimal subvolume layout
- Snapper configured for automatic snapshots
- GRUB integration for snapshot booting
- Timeline and cleanup automation

EOF
}

main() {
  case $BUILD_METHOD in
  "help" | "-h" | "--help")
    show_usage
    exit 0
    ;;
  "mkksiso" | "livemedia-creator" | "manual") ;;
  *)
    print_error "Unknown build method: $BUILD_METHOD"
    show_usage
    exit 1
    ;;
  esac

  print_status "Starting Fedora $FEDORA_VERSION custom ISO build using $BUILD_METHOD method"

  # Preflight checks
  check_dependencies
  validate_kickstart
  download_fedora_iso

  # Build ISO
  case $BUILD_METHOD in
  "mkksiso")
    build_with_mkksiso
    ;;
  "livemedia-creator")
    build_with_livemedia_creator
    ;;
  "manual")
    build_manual
    ;;
  esac

  # Validate result
  test_iso

  print_success "Build completed successfully!"
  print_status "Custom ISO: $OUTPUT_ISO"
  print_status "You can now boot this ISO to install Fedora with Btrfs and Snapper"
  print_warning "Remember to change default passwords in the kickstart file before production use!"
}

# Run main function
main "$@"
