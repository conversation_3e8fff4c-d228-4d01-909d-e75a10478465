d-i debian-installer/locale string en_US
d-i console-setup/ask_detect boolean false
d-i keyboard-configuration/xkb-keymap select us

d-i netcfg/choose_interface select auto
d-i netcfg/get_hostname string debian
d-i netcfg/get_domain string local

d-i mirror/country string manual
d-i mirror/http/hostname string deb.debian.org
d-i mirror/http/directory string /debian
d-i mirror/http/proxy string

d-i clock-setup/utc boolean true
d-i time/zone string Asia/Kolkata
d-i clock-setup/ntp boolean true

d-i partman-auto/method string lvm
d-i partman-lvm/device_remove_lvm boolean true
d-i partman-auto/choose_recipe select atomic
d-i partman-auto/confirm boolean true
d-i partman/confirm_write_new_label boolean true
d-i partman/choose_partition select finish
d-i partman/confirm boolean true

# d-i passwd/user-password-crypted password [hash]
# generate with mkpasswd -m sha-512 (or openssl passwd -6?)

d-i passwd/root-password password root123
d-i passwd/root-password-again password root123
d-i passwd/user-fullname string User
d-i passwd/username string user
d-i passwd/user-password password user123
d-i passwd/user-password-again password user123

tasksel tasksel/first multiselect standard
d-i pkgsel/include string sudo openssh-server curl git
d-i grub-installer/only_debian boolean true
d-i finish-install/reboot_in_progress note
