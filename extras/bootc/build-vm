#!/usr/bin/env bash
set -euo pipefail

mkdir -p output

# sudo podman build -t sway-bootc .

sudo podman run \
  --rm \
  -it \
  --privileged \
  --pull=newer \
  --security-opt label=type:unconfined_t \
  -v ./config.toml:/config.toml:ro \
  -v ./output:/output \
  -v /var/lib/containers/storage:/var/lib/containers/storage \
  quay.io/centos-bootc/bootc-image-builder:latest \
  --type qcow2 \
  --type raw \
  --type iso \
  --rootfs btrfs \
  --use-librepo=True \
  localhost/sway-bootc
