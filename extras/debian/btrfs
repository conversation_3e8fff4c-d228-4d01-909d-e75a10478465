#!/bin/sh

set -euf

TARGET="/target"
TMP_MNT="/tmp/btrfs-root-$$"
trap 'umount "$TMP_MNT" 2>/dev/null; rmdir "$TMP_MNT" 2>/dev/null; exit' EXIT

for cmd in btrfs blkid mountpoint; do
  command -v "$cmd" >/dev/null || {
    echo "Missing $cmd"
    exit 1
  }
done

ROOT_DEV=$(mountpoint -d "$TARGET" 2>/dev/null) || ROOT_DEV=""
if [ -n "$ROOT_DEV" ]; then
  ROOT_DEV="/dev/$(find /dev/block -type b -name "*${ROOT_DEV#*:}*" -exec basename {} \; 2>/dev/null | head -n1)"
fi

if [ -z "$ROOT_DEV" ] || ! [ -b "$ROOT_DEV" ]; then
  ROOT_DEV=$(blkid -t TYPE=btrfs | awk -F: '{print $1}' | head -n1)
fi

[ -b "$ROOT_DEV" ] || {
  echo "Cannot find root Btrfs device"
  exit 1
}

ROOT_UUID=$(blkid -s UUID -o value "$ROOT_DEV") || {
  echo "Cannot get UUID for $ROOT_DEV"
  exit 1
}

mkdir -p "$TMP_MNT"
umount "$TARGET" 2>/dev/null || true
mount -t btrfs -o compress=zstd:1,space_cache=v2 "$ROOT_DEV" "$TMP_MNT" || {
  echo "Failed to mount $ROOT_DEV on $TMP_MNT"
  exit 1
}

##############################################################################
# 3.  Migrate legacy sub-volume names (Trixie uses @rootfs)
##############################################################################
LEGACY_SUBVOL="$TMP_MNT/@rootfs"
NEW_SUBVOL="$TMP_MNT/@"

# If we have the legacy @rootfs subvolume, rename it to @
if [ -d "$LEGACY_SUBVOL" ]; then
  if [ -d "$NEW_SUBVOL" ]; then
    echo "Both @rootfs and @ sub-volumes exist – aborting." >&2
    exit 1
  fi
  
  btrfs subvolume rename "$LEGACY_SUBVOL" "$NEW_SUBVOL" || {
    echo "Failed to rename @rootfs to @"
    exit 1
  }
fi

create_if_missing() {
  [ -d "$1" ] || btrfs subvolume create "$1" || {
    echo "Failed to create subvolume $1"
    exit 1
  }
}

create_if_missing "$TMP_MNT/@"
create_if_missing "$TMP_MNT/@home"
create_if_missing "$TMP_MNT/@var"
create_if_missing "$TMP_MNT/@var_log"
create_if_missing "$TMP_MNT/@opt"
create_if_missing "$TMP_MNT/@srv"
# Note: @tmp is not created as /tmp is typically mounted to RAM (tmpfs) in Debian
create_if_missing "$TMP_MNT/@.snapshots"
create_if_missing "$TMP_MNT/@home.snapshots"

ID=$(btrfs subvolume list "$TMP_MNT" | awk '$NF == "@"{print $2}') || {
  echo "Failed to get subvolume list"
  exit 1
}

[ -n "$ID" ] || {
  echo "Failed to find @ subvolume ID"
  exit 1
}

btrfs subvolume set-default "$ID" "$TMP_MNT" || {
  echo "Failed to set @ as default subvolume"
  exit 1
}

umount "$TMP_MNT"
mount -o "subvol=@,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET" || {
  echo "Failed to mount root subvolume"
  exit 1
}

# POSIX-safe directory creation
# Note: /tmp is mounted to RAM (tmpfs) by default in Debian, so we don't include it here
for d in home opt srv var var/log .snapshots home/.snapshots; do
  mkdir -p "$TARGET/$d"
done

mount -o "subvol=@home,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET/home"
mount -o "subvol=@opt,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET/opt"
mount -o "subvol=@srv,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET/srv"
# /tmp is mounted to RAM (tmpfs) by default in Debian, so no separate subvolume mount
mount -o "subvol=@var,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET/var"
mount -o "subvol=@var_log,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET/var/log"
mount -o "subvol=@.snapshots,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET/.snapshots"
mount -o "subvol=@home.snapshots,compress=zstd:1,space_cache=v2" "$ROOT_DEV" "$TARGET/home/<USER>"

cat >"$TARGET/etc/fstab" <<EOF
# <file system> <mount point> <type> <options> <dump> <pass>
UUID=$ROOT_UUID /               btrfs  subvol=@,defaults,noatime,compress=zstd:1,space_cache=v2 0 1
UUID=$ROOT_UUID /home           btrfs  subvol=@home,defaults,noatime,compress=zstd:1,space_cache=v2 0 2
UUID=$ROOT_UUID /opt            btrfs  subvol=@opt,defaults,noatime,compress=zstd:1,space_cache=v2 0 2
UUID=$ROOT_UUID /srv            btrfs  subvol=@srv,defaults,noatime,compress=zstd:1,space_cache=v2 0 2
UUID=$ROOT_UUID /var            btrfs  subvol=@var,defaults,noatime,compress=zstd:1,space_cache=v2 0 2
UUID=$ROOT_UUID /var/log        btrfs  subvol=@var_log,defaults,noatime,compress=zstd:1,space_cache=v2 0 2
UUID=$ROOT_UUID /.snapshots     btrfs  subvol=@.snapshots,defaults,noatime,compress=zstd:1,space_cache=v2 0 2
UUID=$ROOT_UUID /home/<USER>
EOF

# Note: /tmp is mounted to RAM (tmpfs) by default in Debian, so this may not be needed
# but we keep it for compatibility with systems that might not use tmpfs for /tmp
mkdir -p "$TARGET/etc/tmpfiles.d"
echo "D /tmp 1777 root root 0" >"$TARGET/etc/tmpfiles.d/tmp.conf"

mkdir -p "$TARGET/etc/default/grub.d"
cat >"$TARGET/etc/default/grub.d/99-subvol.cfg" <<'EOF'
GRUB_CMDLINE_LINUX="$GRUB_CMDLINE_LINUX rootflags=subvol=@"
EOF

echo "Btrfs sub-volume layout installed. You may now resume the installer."
