{"nodes": {"flake-compat": {"flake": false, "locked": {"lastModified": 1747046372, "narHash": "sha256-CIVLLkVgvHYbgI2UpXvIIBJ12HWgX+fjA8Xf8PUmqCY=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "9100a0f413b0c601e0533d1d94ffd501ce2e7885", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "home-manager": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1757256385, "narHash": "sha256-WK7tOhWwr15mipcckhDg2no/eSpM1nIh4C9le8HgHhk=", "owner": "nix-community", "repo": "home-manager", "rev": "f35703b412c67b48e97beb6e27a6ab96a084cd37", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "nixos-wsl": {"inputs": {"flake-compat": "flake-compat", "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1757331535, "narHash": "sha256-YYw87rHNMkp6NxT0hThxY5E6zXsQpDtCyWqUNViAmVQ=", "owner": "nix-community", "repo": "NixOS-WSL", "rev": "dedb70d7fa9f06d9bac5e75481af4685415de49c", "type": "github"}, "original": {"owner": "nix-community", "ref": "main", "repo": "NixOS-WSL", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1757068644, "narHash": "sha256-NOrUtIhTkIIumj1E/Rsv1J37Yi3xGStISEo8tZm3KW4=", "owner": "NixOS", "repo": "nixpkgs", "rev": "8eb28adfa3dc4de28e792e3bf49fcf9007ca8ac9", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"home-manager": "home-manager", "nixos-wsl": "nixos-wsl", "nixpkgs": "nixpkgs"}}}, "root": "root", "version": 7}