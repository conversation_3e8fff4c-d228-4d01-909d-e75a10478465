{"nodes": {"darwin": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1758102940, "narHash": "sha256-wwqf3+A8EiqwWpcAaPN20QXJLlpGPpwtLTrzgnngI2o=", "owner": "LnL7", "repo": "nix-darwin", "rev": "ebd0bfc11fc2b5cff37401e9b3703881ad5fabbd", "type": "github"}, "original": {"owner": "LnL7", "repo": "nix-darwin", "type": "github"}}, "flake-compat": {"flake": false, "locked": {"lastModified": 1747046372, "narHash": "sha256-CIVLLkVgvHYbgI2UpXvIIBJ12HWgX+fjA8Xf8PUmqCY=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "9100a0f413b0c601e0533d1d94ffd501ce2e7885", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "home-manager": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1756579987, "narHash": "sha256-duCce8zGsaMsrqqOmLOsuaV1PVIw/vXWnKuLKZClsGg=", "owner": "nix-community", "repo": "home-manager", "rev": "99a69bdf8a3c6bf038c4121e9c4b6e99706a187a", "type": "github"}, "original": {"owner": "nix-community", "repo": "home-manager", "type": "github"}}, "nixos-wsl": {"inputs": {"flake-compat": "flake-compat", "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1757671225, "narHash": "sha256-ZzoQXe7GV7QX3B3Iw59BogmrtHSP5Ig7MAPPD0cOFW4=", "owner": "nix-community", "repo": "NixOS-WSL", "rev": "42666441c3ddf34a8583a77f07a2c7cae32513c3", "type": "github"}, "original": {"owner": "nix-community", "ref": "main", "repo": "NixOS-WSL", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1756542300, "narHash": "sha256-tlOn88coG5fzdyqz6R93SQL5Gpq+m/DsWpekNFhqPQk=", "owner": "NixOS", "repo": "nixpkgs", "rev": "d7600c775f877cd87b4f5a831c28aa94137377aa", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"darwin": "darwin", "flake-utils": "flake-utils", "home-manager": "home-manager", "nixos-wsl": "nixos-wsl", "nixpkgs": "nixpkgs"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}