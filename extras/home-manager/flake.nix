{
  description = "ILM home-manager flake";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";

    nixos-wsl = {
      url = "github:nix-community/NixOS-WSL/main";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    home-manager = {
      url = "github:nix-community/home-manager";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    darwin = {
      url = "github:LnL7/nix-darwin";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs =
    {
      nixpkgs,
      home-manager,
      flake-utils,
      ...
    }@inputs:
    let
      vars = import ./vars.nix;
      hmsVars = import ./hms-vars.nix;

      forAllSystems = flake-utils.lib.eachDefaultSystem;

      mkHmModule = extraImports: {
        home-manager = {
          useGlobalPkgs = true;
          useUserPackages = true;
          extraSpecialArgs = {
            inherit vars inputs;
            imports = [ ./core.nix ] ++ extraImports;
          };
          users.${vars.username} = import ./home.nix;
        };
      };

      mkDarwin =
        imports:
        import ./darwin.nix {
          inherit inputs;
          inherit vars;
          hmModule = mkHmModule imports;
        };

      mkWSL =
        imports:
        import ./wsl.nix {
          inherit inputs;
          inherit vars;
          hmModule = mkHmModule imports;
        };

    in
    {
      nixosConfigurations = {
        wsl = mkWSL [
          ./shell.nix
          ./programs.nix
        ];
      };

      darwinConfigurations = {
        "${vars.host ? "mac"}" = mkDarwin [
          ./programs.nix
        ];
      };
      homeConfigurations =
        let
          mkConfig = system: name: modules: {
            name = "${name}@${system}";
            value = home-manager.lib.homeManagerConfiguration {
              pkgs = nixpkgs.legacyPackages.${system};
              extraSpecialArgs = {
                vars = hmsVars;
                inherit inputs;
              };
              modules = [
                ./home.nix
                ./core.nix
              ]
              ++ modules;
            };
          };

          systems = [
            "x86_64-linux"
            "aarch64-darwin"
          ];
          configs = [
            {
              name = hmsVars.username;
              modules = [ ];
            }
            {
              name = "shell";
              modules = [ ./shell.nix ];
            }
            {
              name = "shell-slim";
              modules = [ ./shell-slim.nix ];
            }
            {
              name = "sys-shell";
              modules = [
                ./sys.nix
                ./shell.nix
              ];
            }
            {
              name = "shell-full";
              modules = [
                ./programs.nix
                ./shell.nix
              ];
            }
          ];
        in
        builtins.listToAttrs (
          builtins.concatLists (
            map (system: map (config: mkConfig system config.name config.modules) configs) systems
          )
        );
    };
}
