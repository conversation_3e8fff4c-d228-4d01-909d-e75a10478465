{
  description = "ILM home-manager flake";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";

    nixos-wsl = {
      url = "github:nix-community/NixOS-WSL/main";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    home-manager = {
      url = "github:nix-community/home-manager";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    darwin = {
      url = "github:LnL7/nix-darwin";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs =
    {
      nixpkgs,
      home-manager,
      flake-utils,
      ...
    }@inputs:
    let
      vars = import ./vars.nix;

      mkHmModule = extraImports: {
        home-manager = {
          useGlobalPkgs = true;
          useUserPackages = true;
          extraSpecialArgs = {
            inherit vars inputs;
            imports = [ ./core.nix ] ++ extraImports;
          };
          users.${vars.username} = import ./home.nix;
        };
      };

      mkDarwin =
        imports:
        import ./darwin.nix {
          inherit inputs;
          inherit vars;
          hmModule = mkHmModule imports;
        };

      mkWSL =
        imports:
        import ./wsl.nix {
          inherit inputs;
          inherit vars;
          hmModule = mkHmModule imports;
        };

    in
    {
      nixosConfigurations = {
        wsl = mkWSL [
          ./shell.nix
          ./programs.nix
        ];
      };

      darwinConfigurations = {
        "${vars.host ? "mac"}" = mkDarwin [
          ./programs.nix
        ];
      };
      homeConfigurations =
        let
          vars = import ./hms-vars.nix;
        in
        {
          # Linux configurations
          "${vars.username}@x86_64-linux" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.x86_64-linux;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
            ];
          };

          "shell@x86_64-linux" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.x86_64-linux;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./shell.nix
            ];
          };

          "shell-slim@x86_64-linux" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.x86_64-linux;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./shell-slim.nix
            ];
          };

          "sys-shell@x86_64-linux" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.x86_64-linux;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./sys.nix
              ./shell.nix
            ];
          };

          "shell-full@x86_64-linux" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.x86_64-linux;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./programs.nix
              ./shell.nix
            ];
          };

          # Darwin configurations
          "${vars.username}@aarch64-darwin" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.aarch64-darwin;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
            ];
          };

          "shell@aarch64-darwin" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.aarch64-darwin;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./shell.nix
            ];
          };

          "shell-slim@aarch64-darwin" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.aarch64-darwin;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./shell-slim.nix
            ];
          };

          "sys-shell@aarch64-darwin" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.aarch64-darwin;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./sys.nix
              ./shell.nix
            ];
          };

          "shell-full@aarch64-darwin" = home-manager.lib.homeManagerConfiguration {
            pkgs = nixpkgs.legacyPackages.aarch64-darwin;
            extraSpecialArgs = { inherit vars inputs; };
            modules = [
              ./home.nix
              ./core.nix
              ./programs.nix
              ./shell.nix
            ];
          };
        };
    };
}
