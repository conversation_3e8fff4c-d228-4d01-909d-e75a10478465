{
  description = "ILM home-manager flake";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";

    nixos-wsl = {
      url = "github:nix-community/NixOS-WSL/main";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    home-manager = {
      url = "github:nix-community/home-manager";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    darwin = {
      url = "github:LnL7/nix-darwin";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs =
    {
      nixpkgs,
      home-manager,
      flake-utils,
      ...
    }@inputs:
    let
      vars = import ./vars.nix;

      mkHmModule = extraImports: {
        home-manager = {
          useGlobalPkgs = true;
          useUserPackages = true;
          extraSpecialArgs = {
            inherit vars inputs;
            imports = [ ./core.nix ] ++ extraImports;
          };
          users.${vars.username} = import ./home.nix;
        };
      };

      mkDarwin =
        imports:
        import ./darwin.nix {
          inherit inputs;
          inherit vars;
          hmModule = mkHmModule imports;
        };

      mkWSL =
        imports:
        import ./wsl.nix {
          inherit inputs;
          inherit vars;
          hmModule = mkHmModule imports;
        };

    in
    {
      nixosConfigurations = {
        wsl = mkWSL [
          ./shell.nix
          ./programs.nix
        ];
      };

      darwinConfigurations = {
        "${vars.host ? "mac"}" = mkDarwin [
          ./programs.nix
        ];
      };
      homeConfigurations =
        let
          vars = import ./hms-vars.nix;

          mkHomeForSystem =
            system:
            let
              pkgs = nixpkgs.legacyPackages.${system};

              mkHome =
                {
                  vars,
                  pkgs,
                  modules ? [ ], # os modules
                  imports ? [ ], # home-manager modules
                }:
                home-manager.lib.homeManagerConfiguration {
                  inherit pkgs;
                  extraSpecialArgs = {
                    inherit vars inputs;
                    imports = [ ./core.nix ] ++ imports;
                  };
                  modules = [ ./home.nix ] ++ modules;
                };

              mkPrograms =
                { vars, pkgs, ... }@args:
                let
                  modules = [ ./programs.nix ] ++ (args.modules or [ ]);
                  imports = args.imports or [ ];
                in
                mkHome {
                  inherit
                    vars
                    pkgs
                    modules
                    imports
                    ;
                };
            in
            {
              "${vars.username}" = mkHome { inherit vars pkgs; };

              shell-slim = mkHome {
                inherit vars pkgs;
                modules = [ ./shell-slim.nix ];
              };

              shell = mkHome {
                inherit vars pkgs;
                modules = [ ./shell.nix ];
              };

              sys-shell = mkHome {
                inherit vars pkgs;
                modules = [
                  ./sys.nix
                  ./shell.nix
                ];
              };

              shell-full = mkPrograms {
                inherit vars pkgs;
                modules = [ ./shell.nix ];
              };
            };

          # Create configurations for each supported system
          linuxConfigs = mkHomeForSystem "x86_64-linux";
          darwinConfigs = mkHomeForSystem "aarch64-darwin";

          # Add system suffixes to avoid conflicts
          addSuffix =
            suffix: configs:
            builtins.listToAttrs (
              map (name: {
                name = "${name}-${suffix}";
                value = configs.${name};
              }) (builtins.attrNames configs)
            );
        in
        linuxConfigs
        // darwinConfigs
        // (addSuffix "linux" linuxConfigs)
        // (addSuffix "darwin" darwinConfigs);
    };
}
