#! /usr/bin/env bash

has_cmd virsh || return 1

virt-docker() {
  local VM_NAME=${1:-docker}
  slog "Creating Docker VM: $VM_NAME"
  sleep 1
  vm-create --distro debian --name "$VM_NAME" --docker
}

virt-incus() {
  local VM_NAME=${1:-incus}
  slog "Creating Incus VM: $VM_NAME"
  sleep 1
  vm-create --distro debian --name "$VM_NAME" --dotfiles incus
}

virt-nix() {
  local VM_NAME=${1:-nix}
  slog "Creating Debian VM: $VM_NAME"
  sleep 1
  vm-create --distro debian --name "$VM_NAME" --nix
}

virt-dev() {
  local VM_NAME=${1:-dev}
  slog "Creating Dev VM: $VM_NAME"
  sleep 1
  vm-create --distro debian --name "$VM_NAME" --docker --brew
}

virt-ilm() {
  local VM_NAME=${1:-ilm}
  slog "Creating ilm VM: $VM_NAME"
  sleep 1
  vm-create --distro debian --name "$VM_NAME" --dotfiles min
}

virt-exec() {
  if [[ $# -ne 3 ]]; then
    fail "Usage: virt-exec <vm-name> <group>"
    return 1
  fi

  local username="$1"
  local vm_name="$2"
  local extra_arg="$3"

  # shellcheck disable=SC2029
  ssh "$username@$vm_name" "bash -c \"\$(curl -sSL ${ILM_SETUP_URL})\" -- $extra_arg"

  #     virsh qemu-agent-command "$vm_name" \
  #         "$(jq -nc --arg cmd "bash -c $cmd" '{
  #     execute: "guest-exec",
  #     arguments: {
  #       path: "/usr/bin/env",
  #       arg: ["bash","-c",$cmd],
  #       "capture-output": true
  #     }
  #   }')"
}
