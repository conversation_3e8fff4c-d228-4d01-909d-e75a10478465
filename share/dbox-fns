#!/usr/bin/env bash

# shellcheck disable=SC2120

export BOXES_DIR=${USE_BOXES_DIR:-${BOXES_DIR}}

has_cmd distrobox || return

dbox_enter() {
  local CONTAINER_NAME=${1}
  shift
  distrobox enter -nw --clean-path --name "${CONTAINER_NAME}" -- "$@"
}

dbox_exec() {
  local CONTAINER_NAME=${1}
  shift

  dbox_enter "${CONTAINER_NAME}" -- "$@"
}

dbox_bash_exec() {
  local CONTAINER_NAME=${1}
  shift

  dbox_enter "${CONTAINER_NAME}" -- bash -c "$*"
}

dbox_create() {
  local CONTAINER_NAME=${1}
  local IMAGE=${2}
  shift 2

  if distrobox list | grep -q "^${CONTAINER_NAME}$"; then
    slog "Distrobox ${CONTAINER_NAME} already exists, skipping creation"
    return 0
  fi

  if dir_exists "${BOXES_DIR}/${CONTAINER_NAME}"; then
    fail "Directory ${BOXES_DIR}/${CONTAINER_NAME} already exists, refusing to create distrobox"
    return 1
  fi

  slog "Creating Ubuntu distrobox: ${CONTAINER_NAME}"
  if distrobox create \
    --hostname "${CONTAINER_NAME}" \
    --yes \
    --image "$IMAGE" \
    --home "${BOXES_DIR}/${CONTAINER_NAME}" \
    --name "$CONTAINER_NAME" \
    "$@"; then
    slog "Distrobox ${CONTAINER_NAME} created successfully"
    slog "Use dboxe ${CONTAINER_NAME} to enter"
    return 0
  else
    fail "Failed to create distrobox ${CONTAINER_NAME}"
    return 1
  fi
}

dbox_create_root() {
  local CONTAINER_NAME=${1}
  local IMAGE=${2}
  shift 2

  dbox_create "$CONTAINER_NAME" "$IMAGE" --root "$@"
}

dbox-ubuntu() {
  local CONTAINER_NAME=${1:-ubuntu}
  dbox_create "${CONTAINER_NAME}" ubuntu:latest
}

dbox-arch() {
  local CONTAINER_NAME=${1:-arch}
  dbox_create "${CONTAINER_NAME}" archlinux:latest
}

dbox-fedora-minimal() {
  local CONTAINER_NAME=${1:-fedora-minimal}
  dbox_create "${CONTAINER_NAME}" registry.fedoraproject.org/fedora-minimal:latest
}

dbox-debian-slim() {
  local CONTAINER_NAME=${1:-debian-slim}
  dbox_create "${CONTAINER_NAME}" debian:trixie-slim
}

dbox-fedora() {
  local CONTAINER_NAME=${1:-fedora}
  dbox_create "${CONTAINER_NAME}" fedora:latest
}

dbox-centos() {
  local CONTAINER_NAME=${1:-centos}
  dbox_create "${CONTAINER_NAME}" centos:latest
}

dbox-debian() {
  local CONTAINER_NAME=${1:-debian}
  dbox_create "${CONTAINER_NAME}" debian:latest
}

dbox-rocky() {
  local CONTAINER_NAME=${1:-rocky}
  dbox_create "${CONTAINER_NAME}" rockylinux:9
}

dbox-tw() {
  local CONTAINER_NAME=${1:-tw}
  dbox_create "${CONTAINER_NAME}" opensuse/tumbleweed
}

dbox-bluefin() {
  local CONTAINER_NAME=${1:-bluefin-cli}
  dbox_create "${CONTAINER_NAME}" ghcr.io/ublue-os/bluefin-cli
}

dbox-wolfi() {
  local CONTAINER_NAME=${1:-wolfi-ublue}
  dbox_create "${CONTAINER_NAME}" ghcr.io/ublue-os/wolfi-toolbox
}

dbox-virt-manager() {
  local CONTAINER_NAME=${1:-virt-manager}
  local PKGS=(
    openssh-server
    patterns-server-kvm_server
    patterns-server-kvm_tools
    qemu-extra
    qemu-linux-user
    qemu-hw-display-virtio-gpu-pci
    qemu-hw-display-virtio-gpu
  )
  local services=(
    sshd.service
    virtqemud.socket
    virtnetworkd.socket
    virtstoraged.socket
    virtnodedevd.socket
  )
  dbox_create_root "${CONTAINER_NAME}" registry.opensuse.org/opensuse/distrobox:latest \
    --pull \
    --init \
    --unshare-all \
    --additional-flags "-p 2222:22" \
    --init-hooks "zypper in -y --no-recommends ${PKGS[*]} && systemctl enable ${services[*]} && usermod -aG libvirt $USER"
}

dbox-fedora-virt-manager() {
  local CONTAINER_NAME=${1:fedora-virt-manager}
  local PKGS=(
    openssh-server
    libvirt
    virt-install
    virt-manager
  )
  local services=(
    sshd.service
    virtqemud.socket
    virtnetworkd.socket
    virtstoraged.socket
    virtnodedevd.socket
  )
  dbox_create_root "${CONTAINER_NAME}" registry.fedoraproject.org/fedora:latest \
    --pull --init --unshare-all \
    --additional-flags "-p 2222:22" \
    --init-hooks "dnf install -y --skip-unavailable ${PKGS[*]} && systemctl enable ${services[*]} && usermod -aG libvirt $USER"
}

dbox-docker-base() {
  local CONTAINER_NAME=${1:-docker-base}

  dbox_create_root "${CONTAINER_NAME}" fedora:latest \
    --additional-packages "systemd docker" \
    --init \
    --unshare-all

    dbox_exec "${CONTAINER_NAME}" --root sudo systemctl enable --now docker
    dbox_exec "${CONTAINER_NAME}" --root sudo usermod -aG docker "$USER"
}

dbox-docker-slim() {
  local CONTAINER_NAME=${1:-docker}
  slog "Creating Docker distrobox: ${CONTAINER_NAME}"
  sudo mkdir -p /var/lib/docker

  dbox_create_root "${CONTAINER_NAME}" ghcr.io/ublue-os/docker-distrobox:latest \
    --init --unshare-all --no-entry \
    --volume /var/lib/docker \
    --additional-packages "systemd libpam-systemd"
}

dbox-docker() {
  local CONTAINER_NAME=${1:-docker}
  dbox-docker-slim "${CONTAINER_NAME}"

  distrobox enter -nw --clean-path --root --name "${CONTAINER_NAME}" -- bash -c "$(curl -sSL "${ILM_SETUP_URL}")" -- dbox-docker-dev
}

dbox-incus() {
  local CONTAINER_NAME=${1:-incus}
  slog "Creating Incus distrobox: ${CONTAINER_NAME}"
  sudo mkdir -p /var/lib/incus

  dbox_create_root "${CONTAINER_NAME}" ghcr.io/ublue-os/incus-distrobox:latest \
    --init --unshare-all --no-entry \
    --volume /var/lib/incus:/var/lib/incus \
    --volume /lib/modules:/lib/modules:ro \
    --additional-packages "systemd libpam-systemd"
}

dbox-alpine-edge-init() {
  local CONTAINER_NAME=${1:-alpine-edge-init}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" quay.io/toolbx-images/alpine-toolbox:edge \
    --init \
    --additional-packages "openrc"
}

dbox-alpine-init() {
  local CONTAINER_NAME=${1:-alpine-init}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" quay.io/toolbx-images/alpine-toolbox:latest \
    --init --additional-packages "openrc"
}

dbox-debian-init() {
  local CONTAINER_NAME=${1:-debian-init}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" debian:latest \
    --init \
    --additional-packages "systemd libpam-systemd"
}

dbox-ubuntu-init() {
  local CONTAINER_NAME=${1:-ubuntu-init}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" ubuntu:latest \
    --init \
    --additional-packages "systemd libpam-systemd"
}

dbox-arch-init() {
  local CONTAINER_NAME=${1:-arch-init}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" archlinux:latest \
    --yes \
    --init \
    --additional-packages "systemd"
}

dbox-tw-init() {
  local CONTAINER_NAME=${1:-tw-init}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" opensuse/tumbleweed \
    --init \
    --additional-packages "systemd"
}

dbox-fedora-init() {
  local CONTAINER_NAME=${1:-fedora-init}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" fedora:latest \
    --init \
    --additional-packages "systemd"
}

dbox-alpine() {
  local CONTAINER_NAME=${1:-alpine}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" quay.io/toolbx-images/alpine-toolbox:latest \
    --additional-packages "gcc libc-dev make gzip zsh git curl neovim tmux ripgrep luarocks fzf eza zoxide github-cli delta bat trash-cli"
}

dbox-alpine-edge() {
  local CONTAINER_NAME=${1:-alpine-edge}
  slog "Creating distrobox $CONTAINER_NAME"

  dbox_create "${CONTAINER_NAME}" \
    quay.io/toolbx-images/alpine-toolbox:edge \
    --additional-packages "gcc libc-dev make gzip zsh git curl neovim tmux ripgrep luarocks fzf eza zoxide github-cli delta bat trash-cli"
}

dbox-dev-default() {

  distrobox create --yes
  distrobox enter -nw --clean-path -- bash -c "$(curl -sSL "${ILM_SETUP_URL}")" -- dbox-dev-atomic
  slog "Default distrobox created!"
}

# shellcheck disable=SC2120
dbox-dev() {
  slog "Creating default distrobox"

  local os
  os=${1:-fedora-init}
  local CONTAINER_NAME=${2:-ilm}

  if [[ ! "$os" =~ ^(ubuntu|debian|arch|tw|fedora)-init$ ]]; then
    fail "Invalid OS type: $os"
    slog "Valid options are: ubuntu-init, debian-init, arch-init, tw-init, fedora-init"
    return 1
  fi

  "dbox-$os" "$CONTAINER_NAME"

  if distrobox enter -nw --clean-path --name "${CONTAINER_NAME}" -- bash -c "$(curl -sSL "${ILM_SETUP_URL}")" -- dbox-dev-atomic; then
    slog "Distrobox ${CONTAINER_NAME} setup successfully"
    slog "Use dboxe ${CONTAINER_NAME} to enter"
    return 0
  else
    fail "Failed to setup distrobox ${CONTAINER_NAME}"
    return 1
  fi
}

dbox-create-main-install() {
  if [[ $# -lt 2 ]]; then
    fail "Usage: dbox-create-main-install <distro> <mainstall> [name]"
    return 1
  fi
  local distro=$1
  local mainstall=$2
  local name=${3:-"${distro}-${mainstall}-test"}

  "dbox-$distro-init" "$name"
  distrobox enter -nw --clean-path --name "$name" -- bash -c "$(curl -sSL "${ILM_SETUP_URL}")" -- "$mainstall"
}

dbox-create-group-install() {
  if [[ $# -lt 2 ]]; then
    fail "Usage: dbox-create-group-install <distro> <groupstall> [name]"
    return 1
  fi
  local distro=$1
  local groupstall=$2
  local name=${3:-"${distro}-${groupstall}-test"}

  "dbox-$distro-init" "$name"
  distrobox enter -nw --clean-path --name "$name" -- bash -c "$(curl -sSL "${ILM_SETUP_URL}")"
  distrobox enter -nw --clean-path --name "$name" -- ilmg "@$groupstall"
}

dbox-ublue-all() {
  slog "Creating Bluefin, Wolfi and Ublue distroboxes"
  dbox-bluefin
  dbox-wolfi
  dbox-docker
  dbox-incus

  distrobox create \
    --hostname ubuntu-ublue \
    --yes \
    --image ghcr.io/ublue-os/ubuntu-toolbox:latest \
    --init \
    --additional-packages "systemd libpam-systemd" \
    --home "${BOXES_DIR}"/ubuntu-ublue \
    --name ubuntu-ublue

  distrobox create \
    --hostname fedora-ublue \
    --yes \
    --image ghcr.io/ublue-os/fedora-toolbox:latest \
    --init \
    --additional-packages "systemd" \
    --home "${BOXES_DIR}"/fedora-ublue \
    --name fedora-ublue

  distrobox create \
    --hostname arch-ublue \
    --yes \
    --image ghcr.io/ublue-os/arch-distrobox:latest \
    --init \
    --additional-packages "systemd" \
    --home "${BOXES_DIR}"/arch-ublue \
    --name arch-ublue

  slog "Creating Bluefin, Wolfi and Ublue distroboxes done!"
}

dbox-alpine-toolbox() {
  dbox_create alpine-toolbox quay.io/toolbx-images/alpine-toolbox:latest
}

dbox-arch-toolbox() {
  dbox_create arch-toolbox quay.io/toolbx/arch-toolbox:latest \
    --init \
    --additional-packages "systemd"
}

dbox-fedora-toolbox() {
  dbox_create fedora-toolbox quay.io/fedora/fedora-toolbox:42 \
    --init \
    --additional-packages "systemd"
}

dbox-centos-toolbox() {
  dbox_create centos-toolbox quay.io/toolbx-images/centos-toolbox:latest \
    --init \
    --additional-packages "systemd"
}

dbox-debian-toolbox() {
  dbox_create debian-toolbox quay.io/toolbx-images/debian-toolbox:latest \
    --init \
    --additional-packages "systemd"
}

dbox-rockylinux-toolbox() {
  dbox_create rockylinux-toolbox quay.io/toolbx-images/rockylinux-toolbox:latest \
    --init \
    --additional-packages "systemd"
}

dbox-ubuntu-toolbox() {
  dbox_create ubuntu-toolbox quay.io/toolbx/ubuntu-toolbox:latest \
    --init \
    --additional-packages "systemd libpam-systemd"
}

dbox-toolbox-all() {
  dbox-alpine-toolbox
  dbox-arch-toolbox
  dbox-fedora-toolbox
  dbox-centos-toolbox
  dbox-debian-toolbox
  dbox-rockylinux-toolbox
  dbox-ubuntu-toolbox
}

dbox-nvidia-container-toolkit() {
  if has_cmd podman; then
    dbox_create example-nvidia-toolkit docker.io/nvidia/cuda \
      --additional-flags "--gpus all"
  elif has_cmd docker; then
    dbox_create example-nvidia-toolkit docker.io/nvidia/cuda \
      --additional-flags "--gpus all --device=nvidia.com/gpu=all"
  else
    warn "podman or docker not found"
  fi
}

dbox-to-image() {
  if has_cmd podman; then
    podman container commit -p dbox_name "$1"
    podman save "$1":latest | bzip2 >"$1".tar.bz
  elif has_cmd docker; then
    docker container commit -p dbox_name "$1"
    docker save "${1}:latest" | gzip >"$1".tar.gz
  fi
}

dbox-from-image() {
  local dbox_name=${1:dbox}
  slog "Creating distrobox from image $1"

  if distrobox create \
    --hostname "${dbox_name}" \
    --yes \
    --image "$1":latest \
    --name "$dbox_name"; then
    distrobox enter -nw --clean-path --name "$dbox_name"
    slog "Done creating distrobox from image $1"
  fi
}

dbox-nix() {
  local CONTAINER_NAME=${1:-deb-nix}

  slog "Creating distrobox $CONTAINER_NAME"

  srm "${BOXES_DIR}/${CONTAINER_NAME}"

  if dbox-debian-init "$CONTAINER_NAME"; then
    distrobox enter -nw --clean-path --name "${CONTAINER_NAME}" -- bash -c "$(curl -sSL "${ILM_SETUP_URL}")" -- nix
    slog "Done creating distrobox $CONTAINER_NAME"
  fi
}

dbox-containers() {
  podman ps -a -s
}
