# This is the configuration file for <PERSON><PERSON>.
#
# This template file has been automatically created at the following
# path since <PERSON><PERSON> couldn't find any existing config files on your system:
#
#   ~/.config/ghostty/config
#
# The template does not set any default options, since <PERSON><PERSON> ships
# with sensible defaults for all options. Users should only need to set
# options that they want to change from the default.
#
# Run `ghostty +show-config --default --docs` to view a list of
# all available config options and their default values.
#
# Additionally, each config option is also explained in detail
# on <PERSON><PERSON>'s website, at https://ghostty.org/docs/config.

# Config syntax crash course
# ==========================
# # The config file consists of simple key-value pairs,
# # separated by equals signs.
# font-family = Iosevka
# window-padding-x = 2
#
# # Spacing around the equals sign does not matter.
# # All of these are identical:
# key=value
# key= value
# key =value
# key = value
#
# # Any line beginning with a # is a comment. It's not possible to put
# # a comment after a config option, since it would be interpreted as a
# # part of the value. For example, this will have a value of "#123abc":
# background = #123abc
#
# # Empty values are used to reset config keys to default.
# key =
#
# # Some config options have unique syntaxes for their value,
# # which is explained in the docs for that config option.
# # Just for example:
# resize-overlay-duration = 4s 200ms
# keybind = global:cmd+/=toggle_quick_terminal

# theme = GitHub-Dark-Default
theme = Everforest Dark - Hard
# theme = rose-pine-moon

# theme = catppuccin-mocha
font-family = JetBrainsMono Nerd Font
font-size = 12
background-blur-radius = 20
mouse-hide-while-typing = true
window-decoration = true
macos-option-as-alt = true
background-opacity = 0.90
background-blur-radius = 20
